// src/table/table.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateTableDto } from './dto/create-table.dto';
import { UpdateTableDto } from './dto/update-table.dto';
import { TableStatus } from '@prisma/client';

@Injectable()
export class TableService {
  constructor(private prisma: PrismaService) {}

  async createTable(data: CreateTableDto) {
    // Branch mevcut mu kontrol et
    const branchExists = await this.prisma.branch.findUnique({
      where: { id: data.branchId, deletedAt: null },
    });
    if (!branchExists) {
      throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    // Masa alanı (area) mevcut mu kontrol et (eğer belirtilmiş<PERSON>)
    if (data.areaId) {
      const areaExists = await this.prisma.tableArea.findUnique({
        where: { id: data.areaId, deletedAt: null },
      });
      if (!areaExists) {
        throw new NotFoundException(`Table area with ID "${data.areaId}" not found.`);
      }
    }

    // Aynı şube içinde aynı numarada masa var mı kontrol et
    const existingTable = await this.prisma.table.findUnique({
      where: {
        branchId_number: {
          branchId: data.branchId,
          number: data.number,
        },
      },
    });
    if (existingTable) {
      throw new ConflictException(`Table with number "${data.number}" already exists for this branch.`);
    }

    // QR kodu benzersiz mi kontrol et
    if (data.qrCode) {
        const existingQrCode = await this.prisma.table.findUnique({
            where: { qrCode: data.qrCode, deletedAt: null }
        });
        if (existingQrCode) {
            throw new ConflictException(`QR Code "${data.qrCode}" is already in use by another table.`);
        }
    }

    return this.prisma.table.create({ data });
  }

  async findAllTables(branchId?: string, areaId?: string, status?: string) {
    return this.prisma.table.findMany({
      where: {
        branchId: branchId || undefined,
        areaId: areaId || undefined,
        status: status ? (status as TableStatus) : undefined, // Enum dönüşümü
        deletedAt: null,
      },
      include: {
        branch: { select: { id: true, name: true } },
        area: { select: { id: true, name: true } },
        orders: {
            where: { deletedAt: null, completedAt: null }, // Sadece aktif ve tamamlanmamış siparişler
            select: { id: true, orderNumber: true, status: true, totalAmount: true }
        }
      },
      orderBy: { number: 'asc' },
    });
  }

  async findOneTable(id: string) {
    const table = await this.prisma.table.findUnique({
      where: { id, deletedAt: null },
      include: {
        branch: { select: { id: true, name: true } },
        area: { select: { id: true, name: true } },
        orders: {
            where: { deletedAt: null, completedAt: null },
            select: { id: true, orderNumber: true, status: true, totalAmount: true }
        }
      },
    });
    if (!table) {
      throw new NotFoundException(`Table with ID "${id}" not found.`);
    }
    return table;
  }

  async updateTable(id: string, data: UpdateTableDto) {
    // İlişkili varlıkların varlığını kontrol et (Branch, TableArea)
    if ((data as any).branchId) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: (data as any).branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new NotFoundException(`Branch with ID "${(data as any).branchId}" not found.`);
        }
    }
    if ((data as any).areaId) {
        const areaExists = await this.prisma.tableArea.findUnique({
            where: { id: (data as any).areaId, deletedAt: null },
        });
        if (!areaExists) {
            throw new NotFoundException(`Table area with ID "${(data as any).areaId}" not found.`);
        }
    }

    // Masa numarası güncelleniyorsa unique kontrolü
    if ((data as any).number) {
        const currentTable = await this.findOneTable(id); // Güncel masayı al
        const existingTable = await this.prisma.table.findUnique({
            where: {
                branchId_number: {
                    branchId: (data as any).branchId || currentTable.branchId,
                    number: (data as any).number,
                },
            },
        });
        if (existingTable && existingTable.id !== id) {
            throw new ConflictException(`Table with number "${(data as any).number}" already exists for this branch.`);
        }
    }

    // QR kodu güncelleniyorsa unique kontrolü
    if ((data as any).qrCode) {
        const existingQrCode = await this.prisma.table.findUnique({
            where: { qrCode: (data as any).qrCode, deletedAt: null }
        });
        if (existingQrCode && existingQrCode.id !== id) {
            throw new ConflictException(`QR Code "${(data as any).qrCode}" is already in use by another table.`);
        }
    }

    try {
      return await this.prisma.table.update({
        where: { id, deletedAt: null },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Table with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeTable(id: string) {
    // Bu masaya bağlı aktif sipariş var mı kontrol et
    const activeOrdersCount = await this.prisma.order.count({
        where: { tableId: id, status: { notIn: ['COMPLETED', 'CANCELLED', 'RETURNED'] }, deletedAt: null }
    });

    if (activeOrdersCount > 0) {
        throw new ConflictException(`Table with ID "${id}" cannot be deleted because it has ${activeOrdersCount} active orders.`);
    }

    // Soft delete uygulaması
    try {
      return await this.prisma.table.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), active: false, status: 'UNAVAILABLE' }, // Soft delete ve pasif hale getirme
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Table with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
