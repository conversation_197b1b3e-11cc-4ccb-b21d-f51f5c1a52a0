/** @type {import('tailwindcss').Config} */
export default {
  content: [
    "./index.html",
    "./src/**/*.{js,ts,jsx,tsx}",
  ],
  theme: {
    extend: {
      colors: {
        // Figma Design System Colors
        neutral: {
          white: '#FFFFFF',
          100: '#F5F5F5',
          300: '#C6C7C8',
          400: '#A2A4A4',
          500: '#797B7C',
          700: '#37383A',
          800: '#292C2D',
          black: '#202325',
        },
        primary: {
          100: '#D9E7F7',
          300: '#679DDF',
          400: '#357DD5',
          500: '#025CCA',
          dark: '#1E3043',
        },
        secondary: {
          100: '#F0F8FF',
        },
        success: {
          100: '#DCF7EA',
          500: '#50D794',
          600: '#48C185',
          700: '#40AC76',
          dark: '#2F4B41',
        },
        warning: {
          100: '#FFEDD5',
          600: '#E49527',
        },
        error: {
          500: '#EE4E4F',
        },
      },
      fontFamily: {
        inter: ['Inter', 'sans-serif'],
      },
      fontSize: {
        'body-5': ['12px', { lineHeight: '1.5', fontWeight: '400' }],
        'body-4': ['14px', { lineHeight: '1.5', fontWeight: '400' }],
        'subtitle-5': ['12px', { lineHeight: '1.5', fontWeight: '500' }],
        'subtitle-4': ['14px', { lineHeight: '1.5', fontWeight: '500' }],
        'subtitle-3': ['16px', { lineHeight: '1.5', fontWeight: '500' }],
        'subtitle-1': ['24px', { lineHeight: '1.5', fontWeight: '500' }],
        'title-5': ['12px', { lineHeight: '1.5', fontWeight: '600' }],
        'title-4': ['14px', { lineHeight: '1.5', fontWeight: '600' }],
        'title-3': ['16px', { lineHeight: '1.5', fontWeight: '600' }],
        'large-1': ['36px', { lineHeight: '1.3', fontWeight: '600' }],
      },
    },
  },
  plugins: [],
}

