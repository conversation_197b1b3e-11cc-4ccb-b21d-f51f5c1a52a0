// src/campaign/dto/create-campaign.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsNumber,
  Min,
  IsDateString,
  IsInt,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CampaignType, DiscountType } from '@prisma/client'; // Enum'ları import et

export class CreateCampaignDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  code: string; // Kampanya kodu (benzersiz olmalı)

  @IsString()
  @IsOptional()
  description?: string;

  @IsEnum(CampaignType)
  @IsNotEmpty()
  campaignType: CampaignType;

  @IsEnum(DiscountType)
  @IsOptional() // Sadece DISCOUNT kampanyaları için geçerli olabilir
  discountType?: DiscountType;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  discountValue?: number; // İndirim miktarı veya yüzdesi

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  minOrderAmount?: number; // Minimum sipariş tutarı

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  maxDiscountAmount?: number; // Maksimum indirim tutarı

  @IsDateString()
  @IsNotEmpty()
  startDate: Date;

  @IsOptional()
  @IsDateString()
  endDate?: Date;

  @IsInt()
  @Min(0)
  @IsOptional()
  usageLimit?: number; // Toplam kullanım limiti

  @IsInt()
  @Min(0)
  @IsOptional()
  usageLimitPerUser?: number; // Kullanıcı başına kullanım limiti

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
