"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TaskService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let TaskService = class TaskService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createTask(data) {
        const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
        if (!companyExists) {
            throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
        if (data.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists) {
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
            }
        }
        if (data.assignedToId) {
            const assignedToUser = await this.prisma.user.findUnique({ where: { id: data.assignedToId, deletedAt: null } });
            if (!assignedToUser) {
                throw new common_1.NotFoundException(`Assigned user with ID "${data.assignedToId}" not found.`);
            }
        }
        const createdByUser = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByUser) {
            throw new common_1.NotFoundException(`Creator user with ID "${data.createdBy}" not found.`);
        }
        return this.prisma.task.create({
            data: {
                ...data,
                status: data.status || client_1.$Enums.TaskStatus.PENDING,
                priority: data.priority || client_1.$Enums.TaskPriority.MEDIUM,
                dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
                completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
            },
        });
    }
    async findAllTasks(companyId, branchId, assignedToId, status, priority, startDate, endDate) {
        return this.prisma.task.findMany({
            where: {
                companyId: companyId || undefined,
                branchId: branchId || undefined,
                assignedToId: assignedToId || undefined,
                status: status || undefined,
                priority: priority || undefined,
                createdAt: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
            },
            include: {
                company: { select: { id: true, name: true } },
                branch: { select: { id: true, name: true } },
                assignedTo: { select: { id: true, firstName: true, lastName: true, username: true } },
                createdByUser: { select: { id: true, firstName: true, lastName: true, username: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneTask(id) {
        const task = await this.prisma.task.findUnique({
            where: { id },
            include: {
                company: { select: { id: true, name: true } },
                branch: { select: { id: true, name: true } },
                assignedTo: { select: { id: true, firstName: true, lastName: true, username: true } },
                createdByUser: { select: { id: true, firstName: true, lastName: true, username: true } },
            },
        });
        if (!task) {
            throw new common_1.NotFoundException(`Task with ID "${id}" not found.`);
        }
        return task;
    }
    async updateTask(id, data) {
        const existingTask = await this.findOneTask(id);
        if (existingTask.status === client_1.$Enums.TaskStatus.COMPLETED || existingTask.status === client_1.$Enums.TaskStatus.CANCELLED) {
            throw new common_1.BadRequestException(`Cannot update a task with status "${existingTask.status}".`);
        }
        if (data.branchId && data.branchId !== existingTask.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists) {
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
            }
        }
        if (data.assignedToId && data.assignedToId !== existingTask.assignedToId) {
            const assignedToUser = await this.prisma.user.findUnique({ where: { id: data.assignedToId, deletedAt: null } });
            if (!assignedToUser) {
                throw new common_1.NotFoundException(`Assigned user with ID "${data.assignedToId}" not found.`);
            }
        }
        if (data.completedAt && data.status !== client_1.$Enums.TaskStatus.COMPLETED) {
            data.status = client_1.$Enums.TaskStatus.COMPLETED;
        }
        if (data.status === client_1.$Enums.TaskStatus.COMPLETED && !data.completedAt) {
            data.completedAt = new Date().toISOString();
        }
        try {
            return await this.prisma.task.update({
                where: { id },
                data: {
                    ...data,
                    dueDate: data.dueDate ? new Date(data.dueDate) : undefined,
                    completedAt: data.completedAt ? new Date(data.completedAt) : undefined,
                },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Task with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeTask(id) {
        try {
            return await this.prisma.task.update({
                where: { id },
                data: { status: client_1.$Enums.TaskStatus.CANCELLED, completedAt: new Date() },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Task with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.TaskService = TaskService;
exports.TaskService = TaskService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TaskService);
//# sourceMappingURL=task.service.js.map