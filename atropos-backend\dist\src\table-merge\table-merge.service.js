"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.TableMergeService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let TableMergeService = class TableMergeService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createTableMerge(data) {
        if (data.tableId === data.targetId) {
            throw new common_1.BadRequestException('Main table ID and merged table ID cannot be the same.');
        }
        const mainTable = await this.prisma.table.findUnique({ where: { id: data.tableId, deletedAt: null } });
        const targetTable = await this.prisma.table.findUnique({ where: { id: data.targetId, deletedAt: null } });
        if (!mainTable) {
            throw new common_1.NotFoundException(`Main table with ID "${data.tableId}" not found or deleted.`);
        }
        if (!targetTable) {
            throw new common_1.NotFoundException(`Merged table with ID "${data.targetId}" not found or deleted.`);
        }
        if (mainTable.branchId !== targetTable.branchId) {
            throw new common_1.BadRequestException('Main table and merged table must be in the same branch.');
        }
        const existingMergeForMain = await this.prisma.tableMerge.findFirst({
            where: {
                OR: [
                    { tableId: data.tableId },
                    { targetId: data.tableId }
                ]
            }
        });
        if (existingMergeForMain) {
            throw new common_1.ConflictException(`Main table with ID "${data.tableId}" is already part of another merge.`);
        }
        const existingMergeForTarget = await this.prisma.tableMerge.findFirst({
            where: {
                OR: [
                    { tableId: data.targetId },
                    { targetId: data.targetId }
                ]
            }
        });
        if (existingMergeForTarget) {
            throw new common_1.ConflictException(`Merged table with ID "${data.targetId}" is already part of another merge.`);
        }
        const reverseMergeExists = await this.prisma.tableMerge.findFirst({
            where: { tableId: data.targetId, targetId: data.tableId }
        });
        if (reverseMergeExists) {
            throw new common_1.ConflictException(`Reverse merge already exists between table "${data.targetId}" and "${data.tableId}".`);
        }
        const tableMerge = await this.prisma.tableMerge.create({ data });
        await this.prisma.table.update({
            where: { id: data.tableId },
            data: { status: client_1.TableStatus.MERGED },
        });
        await this.prisma.table.update({
            where: { id: data.targetId },
            data: { status: client_1.TableStatus.MERGED },
        });
        return tableMerge;
    }
    async findAllTableMerges(tableId, targetId) {
        const whereCondition = {};
        if (tableId || targetId) {
            whereCondition.OR = [];
            if (tableId) {
                whereCondition.OR.push({ tableId: tableId });
            }
            if (targetId) {
                whereCondition.OR.push({ targetId: targetId });
            }
        }
        return this.prisma.tableMerge.findMany({
            where: whereCondition,
            include: {
                mainTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
                mergedTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneTableMerge(id) {
        const merge = await this.prisma.tableMerge.findUnique({
            where: { id },
            include: {
                mainTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
                mergedTable: { select: { id: true, number: true, name: true, status: true, branchId: true } },
            },
        });
        if (!merge) {
            throw new common_1.NotFoundException(`Table merge with ID "${id}" not found.`);
        }
        return merge;
    }
    async updateTableMerge(id, data) {
        throw new common_1.ForbiddenException('Updating core fields of a table merge is not allowed. Please delete the existing merge and create a new one.');
    }
    async removeTableMerge(id) {
        try {
            const merge = await this.prisma.tableMerge.delete({
                where: { id },
            });
            await this.prisma.table.update({
                where: { id: merge.tableId },
                data: { status: client_1.TableStatus.EMPTY },
            });
            await this.prisma.table.update({
                where: { id: merge.targetId },
                data: { status: client_1.TableStatus.EMPTY },
            });
            return merge;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Table merge with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.TableMergeService = TableMergeService;
exports.TableMergeService = TableMergeService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], TableMergeService);
//# sourceMappingURL=table-merge.service.js.map