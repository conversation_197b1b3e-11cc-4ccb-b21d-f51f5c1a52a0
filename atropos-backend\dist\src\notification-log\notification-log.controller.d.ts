import { NotificationLogService } from './notification-log.service';
import { CreateNotificationLogDto } from './dto/create-notification-log.dto';
import { UpdateNotificationLogDto } from './dto/update-notification-log.dto';
import { NotificationChannel, NotificationStatus } from '@prisma/client';
export declare class NotificationLogController {
    private readonly notificationLogService;
    constructor(notificationLogService: NotificationLogService);
    create(createNotificationLogDto: CreateNotificationLogDto): Promise<{
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
    findAll(templateId?: string, recipient?: string, channel?: NotificationChannel, status?: NotificationStatus, startDate?: Date, endDate?: Date): Promise<({
        template: {
            id: string;
            name: string;
            code: string;
            channel: import(".prisma/client").$Enums.NotificationChannel;
        };
    } & {
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    })[]>;
    findOne(id: string): Promise<{
        template: {
            id: string;
            name: string;
            code: string;
            channel: import(".prisma/client").$Enums.NotificationChannel;
        };
    } & {
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
    update(id: string, updateNotificationLogDto: UpdateNotificationLogDto): Promise<{
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
}
