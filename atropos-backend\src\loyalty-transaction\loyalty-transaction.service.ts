// src/loyalty-transaction/loyalty-transaction.service.ts
import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateLoyaltyTransactionDto } from './dto/create-loyalty-transaction.dto';
import { UpdateLoyaltyTransactionDto } from './dto/update-loyalty-transaction.dto';
import { LoyaltyTransactionType } from '@prisma/client';
import { LoyaltyCardService } from '../loyalty-card/loyalty-card.service'; // LoyaltyCardService'i import et

@Injectable()
export class LoyaltyTransactionService {
  constructor(
    private prisma: PrismaService,
    private loyaltyCardService: LoyaltyCardService, // LoyaltyCardService'i enjekte et
  ) {}

  async createLoyaltyTransaction(data: CreateLoyaltyTransactionDto) {
    // LoyaltyCard mevcut mu kontrol et
    const loyaltyCard = await this.prisma.loyaltyCard.findUnique({
      where: { id: data.cardId, active: true },
    });
    if (!loyaltyCard) {
      throw new NotFoundException(`Loyalty card with ID "${data.cardId}" not found or not active.`);
    }

    // Eğer orderId belirtilmişse siparişin varlığını kontrol et
    if (data.orderId) {
      const orderExists = await this.prisma.order.findUnique({
        where: { id: data.orderId, deletedAt: null },
      });
      if (!orderExists) {
        throw new NotFoundException(`Order with ID "${data.orderId}" not found.`);
      }
    }
    
    // createdBy kullanıcı var mı kontrol et
    if (data.createdBy) {
        const createdByUser = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByUser) {
            throw new NotFoundException(`User with ID "${data.createdBy}" not found.`);
        }
    }

    let newPointBalance = loyaltyCard.points;
    let newMoneyBalance = parseFloat(loyaltyCard.balance.toString());

    // Transaction tipine göre karttaki puan/bakiye güncelle
    switch (data.type) {
      case LoyaltyTransactionType.EARN_PURCHASE:
      case LoyaltyTransactionType.EARN_BONUS:
      case LoyaltyTransactionType.EARN_CAMPAIGN:
      case LoyaltyTransactionType.EARN_BIRTHDAY:
      case LoyaltyTransactionType.EARN_REFERRAL:
      case LoyaltyTransactionType.TRANSFER_IN:
        if (data.points === undefined || data.points <= 0) {
          throw new BadRequestException('Points must be a positive integer for this transaction type.');
        }
        await this.loyaltyCardService.addPoints(data.cardId, data.points);
        newPointBalance += data.points;
        break;

      case LoyaltyTransactionType.SPEND_DISCOUNT:
      case LoyaltyTransactionType.SPEND_PRODUCT:
      case LoyaltyTransactionType.TRANSFER_OUT:
      case LoyaltyTransactionType.EXPIRE:
        if (data.points === undefined || data.points <= 0) {
          throw new BadRequestException('Points must be a positive integer for this transaction type.');
        }
        await this.loyaltyCardService.spendPoints(data.cardId, data.points); // İçeride yeterli puan kontrolü var
        newPointBalance -= data.points;
        break;

      case LoyaltyTransactionType.LOAD_BALANCE:
        if (data.amount === undefined || data.amount <= 0) {
          throw new BadRequestException('Amount must be a positive number for this transaction type.');
        }
        await this.loyaltyCardService.addBalance(data.cardId, data.amount);
        newMoneyBalance += data.amount;
        break;

      case LoyaltyTransactionType.USE_BALANCE:
        if (data.amount === undefined || data.amount <= 0) {
          throw new BadRequestException('Amount must be a positive number for this transaction type.');
        }
        await this.loyaltyCardService.spendBalance(data.cardId, data.amount); // İçeride yeterli bakiye kontrolü var
        newMoneyBalance -= data.amount;
        break;

      case LoyaltyTransactionType.ADJUSTMENT:
        if (data.points === undefined && data.amount === undefined) {
            throw new BadRequestException('Either points or amount must be provided for ADJUSTMENT transaction type.');
        }
        if (data.points !== undefined) {
            if (data.points > 0) await this.loyaltyCardService.addPoints(data.cardId, data.points);
            else if (data.points < 0) await this.loyaltyCardService.spendPoints(data.cardId, Math.abs(data.points));
            newPointBalance += data.points;
        }
        if (data.amount !== undefined) {
            if (data.amount > 0) await this.loyaltyCardService.addBalance(data.cardId, data.amount);
            else if (data.amount < 0) await this.loyaltyCardService.spendBalance(data.cardId, Math.abs(data.amount));
            newMoneyBalance += data.amount;
        }
        break;
        
      default:
        throw new BadRequestException(`Unsupported loyalty transaction type: ${data.type}`);
    }

    const transaction = await this.prisma.loyaltyTransaction.create({
      data: {
        ...data,
        points: data.points !== undefined ? data.points : 0,
        amount: data.amount !== undefined ? parseFloat(data.amount.toFixed(2)) : undefined,
        pointBalance: newPointBalance,
        moneyBalance: newMoneyBalance,
        baseAmount: data.baseAmount !== undefined ? parseFloat(data.baseAmount.toFixed(2)) : undefined,
        multiplier: data.multiplier !== undefined ? parseFloat(data.multiplier.toFixed(2)) : undefined,
        expiresAt: data.expiresAt || undefined,
        createdBy: data.createdBy,
      },
      include: { card: true, order: true },
    });

    return transaction;
  }

  async findAllLoyaltyTransactions(cardId?: string, orderId?: string, type?: LoyaltyTransactionType, createdBy?: string, startDate?: Date, endDate?: Date) {
    return this.prisma.loyaltyTransaction.findMany({
      where: {
        cardId: cardId || undefined,
        orderId: orderId || undefined,
        type: type || undefined,
        createdBy: createdBy || undefined,
        createdAt: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
        },
      },
      include: {
        card: { select: { id: true, cardNumber: true, customerId: true, cardType: true } },
        order: { select: { id: true, orderNumber: true, totalAmount: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneLoyaltyTransaction(id: string) {
    const transaction = await this.prisma.loyaltyTransaction.findUnique({
      where: { id },
      include: {
        card: { select: { id: true, cardNumber: true, customerId: true, cardType: true } },
        order: { select: { id: true, orderNumber: true, totalAmount: true } },
      },
    });
    if (!transaction) {
      throw new NotFoundException(`Loyalty transaction with ID "${id}" not found.`);
    }
    return transaction;
  }

  async updateLoyaltyTransaction(id: string, data: UpdateLoyaltyTransactionDto) {
    const existingTransaction = await this.findOneLoyaltyTransaction(id);

    // LoyaltyTransaction'lar genellikle finansal kayıtlar gibi güncellenmez.
    // Sadece 'ADJUSTMENT' tipi veya 'description' gibi açıklayıcı alanlar için güncellemeler olabilir.
    // Hassas alanlar (cardId, orderId, type, points, amount) güncellenemez.
    if ((data as any).cardId !== undefined || (data as any).orderId !== undefined || (data as any).type !== undefined ||
        (data as any).points !== undefined || (data as any).amount !== undefined || (data as any).baseAmount !== undefined ||
        (data as any).multiplier !== undefined) {
        throw new ForbiddenException('Cannot update sensitive fields of a loyalty transaction. Only description, expiresAt, and createdBy (if applicable) can be updated.');
    }

    try {
      return await this.prisma.loyaltyTransaction.update({
        where: { id },
        data: {
            description: (data as any).description,
            expiresAt: (data as any).expiresAt || undefined,
            createdBy: (data as any).createdBy,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Loyalty transaction with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeLoyaltyTransaction(id: string) {
    // LoyaltyTransaction modelinde deletedAt alanı yok.
    // Finansal hareketler gibi, sadakat hareketleri de genellikle fiziksel silinmez.
    // Bunun yerine, bir "iptal" veya "düzeltme" hareketi (ADJUSTMENT) oluşturulur.
    // Şimdilik fiziksel silme yapıyoruz, ancak bu endpoint'in erişimi çok kısıtlı olmalıdır.
    try {
      const transaction = await this.prisma.loyaltyTransaction.delete({
        where: { id },
      });

      // Not: Bu silme sonrası, LoyaltyCard'daki puan/bakiye değerlerinin geri alınması gerekir.
      // Bu, karmaşık bir senaryodur ve bu modülün ilk versiyonunda otomatik olarak ele alınmayacaktır.
      // Manuel olarak bir ADJUSTMENT hareketi oluşturarak telafi edilmesi önerilir.

      return transaction;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Loyalty transaction with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
