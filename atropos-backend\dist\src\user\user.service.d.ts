import { PrismaService } from '../prisma/prisma.service';
import { CreateUserDto } from './dto/create-user.dto';
import { UpdateUserDto } from './dto/update-user.dto';
import { User } from '@prisma/client';
export declare class UserService {
    private prisma;
    constructor(prisma: PrismaService);
    findByUsername(username: string): Promise<User | null>;
    findById(id: string): Promise<User | null>;
    findEmployeesForLogin(companyId?: string, branchId?: string): Promise<{
        id: string;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        employeeCode: string | null;
    }[]>;
    createUser(data: CreateUserDto): Promise<{
        id: string;
        phone: string | null;
        email: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        password: string;
        pin: string | null;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        permissions: import("@prisma/client/runtime/library").JsonValue | null;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        refreshToken: string | null;
        failedLoginCount: number;
        lockedUntil: Date | null;
        version: number;
    }>;
    findAllUsers(companyId?: string, branchId?: string): Promise<{
        id: string;
        phone: string | null;
        email: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        permissions: import("@prisma/client/runtime/library").JsonValue;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        lockedUntil: Date | null;
        version: number;
    }[]>;
    findOneUser(id: string): Promise<{
        id: string;
        phone: string | null;
        email: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        permissions: import("@prisma/client/runtime/library").JsonValue;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        lockedUntil: Date | null;
        version: number;
    }>;
    updateUser(id: string, data: UpdateUserDto): Promise<{
        id: string;
        phone: string | null;
        email: string | null;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        companyId: string;
        active: boolean;
        branchId: string | null;
        username: string;
        firstName: string;
        lastName: string;
        avatar: string | null;
        role: import(".prisma/client").$Enums.UserRole;
        permissions: import("@prisma/client/runtime/library").JsonValue;
        employeeCode: string | null;
        hireDate: Date | null;
        birthDate: Date | null;
        nationalId: string | null;
        vehicleType: string | null;
        vehiclePlate: string | null;
        lastLoginAt: Date | null;
        failedLoginCount: number;
        lockedUntil: Date | null;
        version: number;
    }>;
    removeUser(id: string): Promise<{
        id: string;
        deletedAt: Date | null;
        active: boolean;
    }>;
}
