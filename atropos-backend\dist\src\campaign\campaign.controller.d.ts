import { CampaignService } from './campaign.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignType } from '@prisma/client';
export declare class CampaignController {
    private readonly campaignService;
    constructor(campaignService: CampaignService);
    create(createCampaignDto: CreateCampaignDto): Promise<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import(".prisma/client").$Enums.CampaignType;
        discountType: import(".prisma/client").$Enums.DiscountType | null;
        discountValue: import("@prisma/client/runtime/library").Decimal | null;
        minOrderAmount: import("@prisma/client/runtime/library").Decimal | null;
        maxDiscountAmount: import("@prisma/client/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
    findAll(companyId?: string, campaignType?: CampaignType, active?: boolean): Promise<({
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import(".prisma/client").$Enums.CampaignType;
        discountType: import(".prisma/client").$Enums.DiscountType | null;
        discountValue: import("@prisma/client/runtime/library").Decimal | null;
        minOrderAmount: import("@prisma/client/runtime/library").Decimal | null;
        maxDiscountAmount: import("@prisma/client/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import(".prisma/client").$Enums.CampaignType;
        discountType: import(".prisma/client").$Enums.DiscountType | null;
        discountValue: import("@prisma/client/runtime/library").Decimal | null;
        minOrderAmount: import("@prisma/client/runtime/library").Decimal | null;
        maxDiscountAmount: import("@prisma/client/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
    update(id: string, updateCampaignDto: UpdateCampaignDto): Promise<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import(".prisma/client").$Enums.CampaignType;
        discountType: import(".prisma/client").$Enums.DiscountType | null;
        discountValue: import("@prisma/client/runtime/library").Decimal | null;
        minOrderAmount: import("@prisma/client/runtime/library").Decimal | null;
        maxDiscountAmount: import("@prisma/client/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        description: string | null;
        startDate: Date;
        endDate: Date | null;
        campaignType: import(".prisma/client").$Enums.CampaignType;
        discountType: import(".prisma/client").$Enums.DiscountType | null;
        discountValue: import("@prisma/client/runtime/library").Decimal | null;
        minOrderAmount: import("@prisma/client/runtime/library").Decimal | null;
        maxDiscountAmount: import("@prisma/client/runtime/library").Decimal | null;
        usageLimit: number | null;
        usageLimitPerUser: number | null;
    }>;
}
