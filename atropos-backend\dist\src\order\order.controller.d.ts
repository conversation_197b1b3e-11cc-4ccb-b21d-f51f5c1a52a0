import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderStatus, PaymentStatus, OrderType } from '@prisma/client';
export declare class OrderController {
    private readonly orderService;
    constructor(orderService: OrderService);
    create(createOrderDto: CreateOrderDto): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("@prisma/client/runtime/library").Decimal | null;
            productId: string;
            status: import(".prisma/client").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("@prisma/client/runtime/library").Decimal;
            discountRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import(".prisma/client").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import(".prisma/client").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        discountRate: import("@prisma/client/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("@prisma/client/runtime/library").Decimal;
        deliveryFee: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        paidAmount: import("@prisma/client/runtime/library").Decimal;
        changeAmount: import("@prisma/client/runtime/library").Decimal;
        tipAmount: import("@prisma/client/runtime/library").Decimal;
        roundingAmount: import("@prisma/client/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
    findAll(branchId?: string, tableId?: string, customerId?: string, status?: OrderStatus, paymentStatus?: PaymentStatus, orderType?: OrderType, startDate?: string, endDate?: string): Promise<({
        branch: {
            id: string;
            name: string;
        };
        table: {
            number: string;
            id: string;
        } | null;
        customer: {
            id: string;
            phone: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
        waiter: {
            id: string;
            firstName: string;
            lastName: string;
        } | null;
        items: ({
            product: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                companyId: string;
                code: string;
                active: boolean;
                version: number;
                description: string | null;
                image: string | null;
                preparationTime: number | null;
                displayOrder: number;
                showInMenu: boolean;
                categoryId: string;
                taxId: string;
                barcode: string | null;
                shortDescription: string | null;
                images: string[];
                basePrice: import("@prisma/client/runtime/library").Decimal;
                costPrice: import("@prisma/client/runtime/library").Decimal | null;
                profitMargin: import("@prisma/client/runtime/library").Decimal | null;
                trackStock: boolean;
                unit: import(".prisma/client").$Enums.ProductUnit;
                criticalStock: import("@prisma/client/runtime/library").Decimal | null;
                available: boolean;
                sellable: boolean;
                calories: number | null;
                allergens: string[];
                hasVariants: boolean;
                hasModifiers: boolean;
                featured: boolean;
                syncId: string | null;
                lastSyncAt: Date | null;
            };
            variant: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                code: string;
                active: boolean;
                version: number;
                displayOrder: number;
                barcode: string | null;
                costPrice: import("@prisma/client/runtime/library").Decimal | null;
                productId: string;
                sku: string | null;
                price: import("@prisma/client/runtime/library").Decimal;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("@prisma/client/runtime/library").Decimal | null;
            productId: string;
            status: import(".prisma/client").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("@prisma/client/runtime/library").Decimal;
            discountRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import(".prisma/client").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import(".prisma/client").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        discountRate: import("@prisma/client/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("@prisma/client/runtime/library").Decimal;
        deliveryFee: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        paidAmount: import("@prisma/client/runtime/library").Decimal;
        changeAmount: import("@prisma/client/runtime/library").Decimal;
        tipAmount: import("@prisma/client/runtime/library").Decimal;
        roundingAmount: import("@prisma/client/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            id: string;
            name: string;
        };
        table: {
            number: string;
            id: string;
        } | null;
        customer: {
            id: string;
            phone: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
        onlinePlatform: {
            id: string;
            name: string;
        } | null;
        waiter: {
            id: string;
            firstName: string;
            lastName: string;
        } | null;
        courier: {
            id: string;
            firstName: string;
            lastName: string;
        } | null;
        items: ({
            product: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                companyId: string;
                code: string;
                active: boolean;
                version: number;
                description: string | null;
                image: string | null;
                preparationTime: number | null;
                displayOrder: number;
                showInMenu: boolean;
                categoryId: string;
                taxId: string;
                barcode: string | null;
                shortDescription: string | null;
                images: string[];
                basePrice: import("@prisma/client/runtime/library").Decimal;
                costPrice: import("@prisma/client/runtime/library").Decimal | null;
                profitMargin: import("@prisma/client/runtime/library").Decimal | null;
                trackStock: boolean;
                unit: import(".prisma/client").$Enums.ProductUnit;
                criticalStock: import("@prisma/client/runtime/library").Decimal | null;
                available: boolean;
                sellable: boolean;
                calories: number | null;
                allergens: string[];
                hasVariants: boolean;
                hasModifiers: boolean;
                featured: boolean;
                syncId: string | null;
                lastSyncAt: Date | null;
            };
            variant: {
                id: string;
                name: string;
                createdAt: Date;
                updatedAt: Date;
                deletedAt: Date | null;
                code: string;
                active: boolean;
                version: number;
                displayOrder: number;
                barcode: string | null;
                costPrice: import("@prisma/client/runtime/library").Decimal | null;
                productId: string;
                sku: string | null;
                price: import("@prisma/client/runtime/library").Decimal;
            } | null;
        } & {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("@prisma/client/runtime/library").Decimal | null;
            productId: string;
            status: import(".prisma/client").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("@prisma/client/runtime/library").Decimal;
            discountRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        })[];
        payments: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            status: import(".prisma/client").$Enums.PaymentStatus;
            changeAmount: import("@prisma/client/runtime/library").Decimal;
            tipAmount: import("@prisma/client/runtime/library").Decimal;
            orderId: string;
            paymentMethodId: string;
            amount: import("@prisma/client/runtime/library").Decimal;
            approvalCode: string | null;
            referenceNo: string | null;
            maskedCardNumber: string | null;
            cardHolderName: string | null;
            installments: number;
            transactionId: string | null;
            refundAmount: import("@prisma/client/runtime/library").Decimal | null;
            refundReason: string | null;
            refundedAt: Date | null;
            gatewayResponse: import("@prisma/client/runtime/library").JsonValue | null;
            paidAt: Date;
            cashMovementId: string | null;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import(".prisma/client").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import(".prisma/client").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        discountRate: import("@prisma/client/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("@prisma/client/runtime/library").Decimal;
        deliveryFee: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        paidAmount: import("@prisma/client/runtime/library").Decimal;
        changeAmount: import("@prisma/client/runtime/library").Decimal;
        tipAmount: import("@prisma/client/runtime/library").Decimal;
        roundingAmount: import("@prisma/client/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
    update(id: string, updateOrderDto: UpdateOrderDto): Promise<{
        items: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            version: number;
            costPrice: import("@prisma/client/runtime/library").Decimal | null;
            productId: string;
            status: import(".prisma/client").$Enums.OrderItemStatus;
            completedAt: Date | null;
            discountAmount: import("@prisma/client/runtime/library").Decimal;
            discountRate: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
            servedAt: Date | null;
            cancelledAt: Date | null;
            variantId: string | null;
            quantity: import("@prisma/client/runtime/library").Decimal;
            unitPrice: import("@prisma/client/runtime/library").Decimal;
            taxRate: import("@prisma/client/runtime/library").Decimal;
            note: string | null;
            guestName: string | null;
            courseNumber: number | null;
            sentToKitchenAt: Date | null;
            startedAt: Date | null;
            voidReason: string | null;
            voidedBy: string | null;
            printCount: number;
            lastPrintedAt: Date | null;
            orderId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import(".prisma/client").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import(".prisma/client").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        discountRate: import("@prisma/client/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("@prisma/client/runtime/library").Decimal;
        deliveryFee: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        paidAmount: import("@prisma/client/runtime/library").Decimal;
        changeAmount: import("@prisma/client/runtime/library").Decimal;
        tipAmount: import("@prisma/client/runtime/library").Decimal;
        roundingAmount: import("@prisma/client/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        deletedAt: Date | null;
        branchId: string;
        version: number;
        syncId: string | null;
        lastSyncAt: Date | null;
        status: import(".prisma/client").$Enums.OrderStatus;
        completedAt: Date | null;
        orderNumber: string;
        orderCode: string | null;
        orderType: import(".prisma/client").$Enums.OrderType;
        tableId: string | null;
        customerCount: number | null;
        customerId: string | null;
        customerName: string | null;
        customerPhone: string | null;
        deliveryAddress: string | null;
        deliveryNote: string | null;
        paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
        mergeTargetId: string | null;
        splitFromId: string | null;
        subtotal: import("@prisma/client/runtime/library").Decimal;
        discountAmount: import("@prisma/client/runtime/library").Decimal;
        discountRate: import("@prisma/client/runtime/library").Decimal;
        discountReason: string | null;
        serviceCharge: import("@prisma/client/runtime/library").Decimal;
        deliveryFee: import("@prisma/client/runtime/library").Decimal;
        taxAmount: import("@prisma/client/runtime/library").Decimal;
        totalAmount: import("@prisma/client/runtime/library").Decimal;
        paidAmount: import("@prisma/client/runtime/library").Decimal;
        changeAmount: import("@prisma/client/runtime/library").Decimal;
        tipAmount: import("@prisma/client/runtime/library").Decimal;
        roundingAmount: import("@prisma/client/runtime/library").Decimal;
        waiterId: string | null;
        cashierId: string | null;
        courierId: string | null;
        orderNote: string | null;
        kitchenNote: string | null;
        internalNote: string | null;
        orderedAt: Date;
        confirmedAt: Date | null;
        preparingAt: Date | null;
        preparedAt: Date | null;
        servedAt: Date | null;
        deliveredAt: Date | null;
        cancelledAt: Date | null;
        estimatedTime: number | null;
        actualTime: number | null;
        onlinePlatformId: string | null;
        platformOrderId: string | null;
        platformOrderNo: string | null;
    }>;
}
