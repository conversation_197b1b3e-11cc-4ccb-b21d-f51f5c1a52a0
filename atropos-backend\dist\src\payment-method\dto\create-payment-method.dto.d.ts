import { PaymentMethodType } from '@prisma/client';
export declare class CreatePaymentMethodDto {
    companyId: string;
    name: string;
    code: string;
    type: PaymentMethodType;
    commissionRate?: number;
    minAmount?: number;
    maxAmount?: number;
    requiresApproval?: boolean;
    requiresReference?: boolean;
    providerName?: string;
    merchantId?: string;
    terminalId?: string;
    displayOrder?: number;
    active?: boolean;
}
