// src/order/dto/update-order.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateOrderDto, CreateOrderItemDto } from './create-order.dto';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsInt,
  Min,
  IsNumber,
  ValidateNested,
  IsArray,
  ArrayMinSize,
  Max,
} from 'class-validator';
import { OrderStatus, PaymentStatus, OrderType } from '@prisma/client';
import { Type } from 'class-transformer';

// Güncelleme sırasında sipariş kalemleri için DTO
export class UpdateOrderItemDto {
    @IsString()
    @IsOptional()
    id?: string; // Eğer mevcut bir kalemi güncelliyorsak ID'si olur

    @IsString()
    @IsOptional()
    productId?: string;

    @IsString()
    @IsOptional()
    variantId?: string;

    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 3 })
    @Min(0.001)
    @IsOptional()
    quantity?: number;

    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    unitPrice?: number;

    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @IsOptional()
    costPrice?: number;

    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @Max(100)
    @IsOptional()
    discountAmount?: number;

    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @Max(100)
    @IsOptional()
    discountRate?: number;

    @Type(() => Number)
    @IsNumber({ maxDecimalPlaces: 2 })
    @Min(0)
    @Max(100)
    @IsOptional()
    taxRate?: number;

    @IsString()
    @IsOptional()
    note?: string;

    @IsString()
    @IsOptional()
    guestName?: string;

    @IsInt()
    @IsOptional()
    @Min(1)
    courseNumber?: number;

    @IsEnum(OrderStatus) // OrderItemStatus değil OrderStatus (şimdilik)
    @IsOptional()
    status?: OrderStatus; // Sadece genel durum değişikliği gibi düşünülürse
}

export class UpdateOrderDto {
  @IsString()
  @IsOptional()
  branchId?: string;

  @IsEnum(OrderType)
  @IsOptional()
  orderType?: OrderType;

  @IsString()
  @IsOptional()
  tableId?: string;

  @IsInt()
  @IsOptional()
  @Min(1)
  customerCount?: number;

  @IsString()
  @IsOptional()
  customerId?: string;

  @IsString()
  @IsOptional()
  customerName?: string;

  @IsString()
  @IsOptional()
  customerPhone?: string;

  @IsString()
  @IsOptional()
  deliveryAddress?: string;

  @IsString()
  @IsOptional()
  deliveryNote?: string;

  @IsString()
  @IsOptional()
  waiterId?: string;

  @IsString()
  @IsOptional()
  orderNote?: string;

  @IsString()
  @IsOptional()
  kitchenNote?: string;

  @IsString()
  @IsOptional()
  internalNote?: string;

  @IsInt()
  @IsOptional()
  @Min(0)
  estimatedTime?: number;

  @IsString()
  @IsOptional()
  onlinePlatformId?: string;

  @IsString()
  @IsOptional()
  platformOrderId?: string;

  @IsString()
  @IsOptional()
  platformOrderNo?: string;

  @IsOptional()
  @IsEnum(OrderStatus)
  status?: OrderStatus;

  @IsOptional()
  @IsEnum(PaymentStatus)
  paymentStatus?: PaymentStatus;

  @IsString()
  @IsOptional()
  cashierId?: string;

  @IsString()
  @IsOptional()
  courierId?: string;

  // Güncelleme için sipariş kalemleri (ekleme/çıkarma/güncelleme)
  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateOrderItemDto)
  items?: UpdateOrderItemDto[];
}
