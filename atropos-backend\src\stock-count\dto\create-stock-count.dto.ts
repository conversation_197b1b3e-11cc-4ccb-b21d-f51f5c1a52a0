// src/stock-count/dto/create-stock-count.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsDateString,
  IsEnum,
  IsArray,
  ValidateNested,
  ArrayMinSize,
  IsNumber,
  Min,
} from 'class-validator';
import { Type } from 'class-transformer';
import { StockCountType, StockCountStatus } from '@prisma/client'; // Enum'ları import et

// Stok Sayım Kalemi için DTO
export class CreateStockCountItemDto {
  @IsString()
  @IsNotEmpty()
  inventoryItemId: string;

  // systemQuantity, unitCost, totalDifference servis tarafından hesaplanacak

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsNotEmpty()
  countedQuantity: number; // Fiziksel olarak sayılan miktar

  @IsString()
  @IsOptional()
  note?: string; // <PERSON><PERSON> özelinde not
}

export class CreateStockCountDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsDateString()
  @IsNotEmpty()
  countDate: Date; // Sayımın yapıldığı tarih

  @IsEnum(StockCountType)
  @IsNotEmpty()
  countType: StockCountType; // Tam sayım, Kısmi sayım vb.

  @IsEnum(StockCountStatus)
  @IsOptional()
  status?: StockCountStatus; // Varsayılan: DRAFT

  @IsString()
  @IsOptional()
  note?: string; // Sayım genelinde not

  @IsOptional()
  @IsDateString()
  startedAt?: Date;

  @IsOptional()
  @IsDateString()
  completedAt?: Date;

  @IsOptional()
  @IsDateString()
  approvedAt?: Date;

  @IsString()
  @IsNotEmpty()
  createdBy: string; // Sayımı başlatan kullanıcı ID'si

  @IsArray()
  @IsString({ each: true })
  @IsOptional()
  countedBy?: string[]; // Sayımı yapan kullanıcı ID'leri

  @IsString()
  @IsOptional()
  approvedBy?: string; // Sayımı onaylayan kullanıcı ID'si

  // Sayım kalemleri
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateStockCountItemDto)
  items: CreateStockCountItemDto[];
}
