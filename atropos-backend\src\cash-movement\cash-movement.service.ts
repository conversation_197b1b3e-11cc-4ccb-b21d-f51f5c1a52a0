// src/cash-movement/cash-movement.service.ts
import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCashMovementDto } from './dto/create-cash-movement.dto';
import { UpdateCashMovementDto } from './dto/update-cash-movement.dto';
import { CashMovementType } from '@prisma/client'; // CashMovementType enum'ı için

@Injectable()
export class CashMovementService {
  constructor(private prisma: PrismaService) {}

  async createCashMovement(data: CreateCashMovementDto) {
    // Branch ve User var mı kontrol et
    const branchExists = await this.prisma.branch.findUnique({
      where: { id: data.branchId, deletedAt: null },
    });
    if (!branchExists) {
      throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    const userExists = await this.prisma.user.findUnique({
      where: { id: data.userId, deletedAt: null },
    });
    if (!userExists) {
      throw new NotFoundException(`User with ID "${data.userId}" not found.`);
    }

    // Ödeme yöntemi belirtilmişse varlığını kontrol et
    if (data.paymentMethodId) {
        const paymentMethodExists = await this.prisma.paymentMethod.findUnique({
            where: { id: data.paymentMethodId, deletedAt: null }
        });
        if (!paymentMethodExists) {
            throw new NotFoundException(`Payment Method with ID "${data.paymentMethodId}" not found.`);
        }
        // Sadece CASH tipi ödeme yöntemleri burada kullanılmalı
        if (paymentMethodExists.type !== 'CASH') {
            throw new BadRequestException(`Only 'CASH' type payment methods are allowed for cash movements.`);
        }
    }

    // Son kasa bakiyesini al (belirli bir şube veya kasa için)
    // Bu, daha gelişmiş bir CashRegister veya Safe modeli gerektirebilir.
    // Şimdilik sadece bu şube için son kasa hareketine bakalım.
    const latestMovement = await this.prisma.cashMovement.findFirst({
      where: { branchId: data.branchId },
      orderBy: { createdAt: 'desc' },
    });
    const previousBalance = latestMovement ? latestMovement.currentBalance.toNumber() : 0;
    const currentBalance = previousBalance + data.amount;

    // Bakiye eksiye düşerse kontrolü (opsiyonel iş kuralı)
    if (currentBalance < 0 && (data.type === CashMovementType.WITHDRAWAL || data.type === CashMovementType.EXPENSE)) {
        // Örneğin, kasada yeterli para yoksa hata ver
        // throw new BadRequestException(`Insufficient cash balance for this withdrawal/expense. Current balance: ${previousBalance.toFixed(2)}.`);
    }
    
    const cashMovement = await this.prisma.cashMovement.create({
      data: {
        ...data,
        amount: parseFloat(data.amount.toFixed(2)),
        previousBalance: parseFloat(previousBalance.toFixed(2)),
        currentBalance: parseFloat(currentBalance.toFixed(2)),
      },
    });

    return cashMovement;
  }

  async findAllCashMovements(
    branchId?: string,
    userId?: string,
    type?: CashMovementType,
    startDate?: Date,
    endDate?: Date,
  ) {
    return this.prisma.cashMovement.findMany({
      where: {
        branchId: branchId || undefined,
        userId: userId || undefined,
        type: type || undefined,
        createdAt: {
          gte: startDate || undefined,
          lte: endDate || undefined,
        },
      },
      include: {
        branch: { select: { id: true, name: true } },
        user: { select: { id: true, firstName: true, lastName: true } },
      },
      orderBy: { createdAt: 'desc' },
    });
  }

  async findOneCashMovement(id: string) {
    const movement = await this.prisma.cashMovement.findUnique({
      where: { id },
      include: {
        branch: { select: { id: true, name: true } },
        user: { select: { id: true, firstName: true, lastName: true } },
      },
    });
    if (!movement) {
      throw new NotFoundException(`Cash movement with ID "${id}" not found.`);
    }
    return movement;
  }

  async updateCashMovement(id: string, data: UpdateCashMovementDto) {
    // Kasa hareketlerinin güncellenmesi genellikle karmaşıktır
    // Özellikle `amount` veya `type` gibi alanlar değişirse,
    // sonraki hareketlerin `previousBalance` ve `currentBalance` değerleri de etkilenecektir.
    // Bu nedenle, temel bir CRUD yapısı sunulsa da, bu tür güncellemeler gerçek bir sistemde
    // ya hiç yapılmaz (sadece `approvedBy`/`approvedAt` gibi alanlar güncellenir)
    // ya da çok dikkatli, tüm zinciri etkileyecek şekilde yönetilmelidir.
    // Şimdilik sadece `approvedBy` ve `approvedAt` gibi alanların güncellenmesine izin verelim.
    // Diğer alanların güncellenmesi için daha karmaşık bir mantık (örn: bir hareketi "iptal et", yenisini oluştur) gerekir.

    const existingMovement = await this.findOneCashMovement(id);

    if (data.amount !== undefined || data.type !== undefined || data.branchId !== undefined || data.userId !== undefined || data.paymentMethodId !== undefined || data.referenceId !== undefined || data.referenceType !== undefined) {
        throw new ForbiddenException('Only "approvedBy" and "approvedAt" fields can be updated for cash movements. For other changes, consider voiding and re-creating the movement.');
    }

    try {
      return await this.prisma.cashMovement.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Cash movement with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeCashMovement(id: string) {
    // Kasa hareketleri genellikle silinmez, ancak "iptal edildi" veya "geçersiz" olarak işaretlenir.
    // Eğer silinirse, sonraki tüm kasa bakiyeleri tutarsız hale gelecektir.
    // Şemanızda deletedAt yok, bu nedenle fiziksel silme yapıyoruz.
    // Ancak bu çok nadir kullanılmalı veya sadece test/geliştirme ortamında.
    // Üretim ortamında: Loglamak veya "VOIDED" statüsü eklemek daha iyi olabilir.

    // Eğer silme işlemi yapılırsa, sonraki hareketlerin bakiyelerini yeniden hesaplamak gerekir.
    // Bu, çok karmaşık bir işlem olabilir ve şimdilik bu kapsamın dışındadır.
    // Basitçe: Fiziksel silme yapıyoruz, ancak bu durumun risklerini anlıyoruz.

    try {
      const movement = await this.prisma.cashMovement.delete({
        where: { id },
      });
      // Not: Bu silme işlemi sonrası, bu hareketi takip eden tüm hareketlerin bakiyelerinin güncellenmesi gerekir.
      // Bu karmaşık bir senaryodur ve bu modülün ilk versiyonunda ele alınmayacaktır.
      // Alternatif olarak, deletedAt alanı eklenip soft delete yapılabilir.
      // Şemanızda deletedAt alanı olmadığı için fiziksel silme uyguluyorum.

      return movement; // Silinen objeyi dönüyoruz
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Cash movement with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
