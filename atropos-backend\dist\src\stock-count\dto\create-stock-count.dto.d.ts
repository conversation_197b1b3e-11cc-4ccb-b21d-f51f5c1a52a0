import { StockCountType, StockCountStatus } from '@prisma/client';
export declare class CreateStockCountItemDto {
    inventoryItemId: string;
    countedQuantity: number;
    note?: string;
}
export declare class CreateStockCountDto {
    branchId: string;
    countDate: Date;
    countType: StockCountType;
    status?: StockCountStatus;
    note?: string;
    startedAt?: Date;
    completedAt?: Date;
    approvedAt?: Date;
    createdBy: string;
    countedBy?: string[];
    approvedBy?: string;
    items: CreateStockCountItemDto[];
}
