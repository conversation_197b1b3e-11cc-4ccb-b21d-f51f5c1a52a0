HOME SAYFASINI YAPIYORUZ (Dashboard'u Silip "Home" Olarak Düzenliyoruz)

Hari<PERSON>, şimdi uygulamanızın ana sayfası olacak olan "Home" sayfasını oluşturmaya başlayabiliriz. Mevcut "Dashboard" sayfasını "Home" olarak yeniden adlandırıp içeriğini güncelleyeceğiz. Figma tasarımlarını referans alarak bu sayfayı işlevsel hale getireceğiz.

**Hedef:**

* Mevcut "Dashboard" bileşenini "Home" olarak yeniden adlandırmak.
* Figma tasarımlarındaki gibi bir genel bakış (Total Earning, In Progress, Waiting List) ve sipariş durumlarını gösteren bir düzen oluşturmak.
* "In Progress" sekmesinde devam eden siparişleri listelemek (Masa ID, müşteri adı, sipariş kalem sayısı, durum bilgisi).
* "Waiting for Payment" sekmesinde ödeme bekleyen siparişleri listelemek (Masa ID, müşteri adı, sipariş kalem sayısı, "Pay Now" butonu).
* Popüler ürünler ve tükenen ürünler gibi ek bilgileri göstermek (ilerleyen aşamalarda).
* Arama çubuğu ile siparişleri arama işlevselliği eklemek.

**Görevler:**

**1. Frontend Geliştirmeleri (`atropos-frontend-desktop/src/pages/`)**

* **1.1. Dosya Yeniden Adlandırma:**
    * `src/pages` klasöründe eğer bir `DashboardPage.tsx` veya benzeri bir dosya varsa, bu dosyayı `HomePage.tsx` olarak yeniden adlandırın.
    * İlgili dosya içindeki bileşen adını (`DashboardPage`) `HomePage` olarak güncelleyin.
    * Rota tanımlamalarınızda (örneğin `src/App.tsx` veya bir rota yapılandırma dosyanız varsa) `/dashboard` yolunu `/home` olarak veya ana sayfa (`/`) olarak güncelleyin ve `HomePage` bileşenine yönlendirin.

* **1.2. Temel Yapıyı Oluşturma (`src/pages/HomePage.tsx`):**
    * Figma tasarımlarına göre sayfanın ana yapısını oluşturun. Bu genellikle şu bölümleri içerir:
        * Üst kısımda:
            * Sol üstte "Aleo Resto" ve şube adı.
            * Ortada bir arama çubuğu (sipariş arama için).
            * Sağ üstte bildirim ikonu (adet göstergesi ile), kullanıcı bilgisi ve menü.
            * "Good Morning, [Kullanıcı Adı]" metni ve güncel tarih/saat.
        * Orta kısımda:
            * Genel bakış kartları (Total Earning, In Progress, Waiting List) ve her birinin altında sayısal değerler ve önceki güne göre değişim yüzdesi.
            * Sağ tarafta "In Progress" ve "Waiting for Payment" sekmeleri olan bir bölüm.
        * Alt kısımda (ilerleyen aşamalarda):
            * Popüler ürünler ve tükenen ürünler listeleri.

* **1.3. Genel Bakış Kartlarını Oluşturma (`src/components/OverviewCard.tsx` veya `src/pages/HomePage.tsx` içinde ayrı bölümler`):**
    * "Total Earning", "In Progress" ve "Waiting List" için ayrı UI bileşenleri oluşturun. Bu bileşenler:
        * Başlık (örn. "Total Earning").
        * Değer (örn. "$526").
        * Trend göstergesi (örn. "****% than yesterday").
        * Figma tasarımlarındaki ikonları içermeli.
        * Bu bilgileri backend'den alacak API çağrıları için hazırlık yapın (şimdilik statik veri kullanabilirsiniz).

* **1.4. Sipariş Listesi Sekmelerini Oluşturma:**
    * İki ana sekme oluşturun: "In Progress" ve "Waiting for Payment". Bu sekmeler arasında geçiş yapmak için bir UI bileşeni (örn. `Tabs` veya basit butonlar) kullanın.
    * **"In Progress" Sekmesi:**
        * Devam eden siparişleri listeleyin. Her bir sipariş için:
            * Masa ID (örn. A9, A5, A3, TA, A6, A12, A7).
            * Müşteri Adı (örn. Adam Hamzah, Nina Renard vb.).
            * Sipariş kalem sayısı (örn. 8 Items, 4 Items vb.).
            * Durum bilgisi (örn. Ready to serve, Cooking Now, In the Kitchen). Duruma göre farklı görsel işaretler (örn. yeşil nokta "Ready", turuncu "Cooking").
    * **"Waiting for Payment" Sekmesi:**
        * Ödeme bekleyen siparişleri listeleyin. Her bir sipariş için:
            * Masa ID (örn. A13, A15, A4, A2, A10, A11, A14).
            * Müşteri Adı (örn. David Owens, Olivia Mason vb.).
            * Sipariş kalem sayısı (örn. 5 Items, 4 Items vb.).
            * "Pay Now" adında bir buton. Bu butona tıklandığında ödeme işlemine yönlendirme yapılacak (şimdilik sadece bir placeholder olabilir).

* **1.5. Veri Bağlantısı (Backend Entegrasyonu):**
    * Backend'de aşağıdaki endpoint'lerin olduğunu varsayarak (gerekirse backend ekibiyle görüşün veya bu endpoint'leri geliştirin):
        * `/api/dashboard/overview`: Toplam kazanç, devam eden sipariş sayısı, ödeme bekleyen sipariş sayısı gibi genel bakış bilgilerini dönecek.
        * `/api/orders?status=in_progress`: Devam eden siparişlerin listesini dönecek.
        * `/api/orders?status=waiting_for_payment`: Ödeme bekleyen siparişlerin listesini dönecek.
    * `useEffect` hook'larını kullanarak `HomePage` bileşeni yüklendiğinde bu API endpoint'lerine istek gönderin ve dönen verileri state'lerde saklayarak UI'ı güncelleyin.
    * Sipariş listeleri için gerekli veri yapılarını (masa ID, müşteri adı, sipariş kalem sayısı, durum vb.) backend'den doğru şekilde aldığınızdan emin olun.

* **1.6. Stil ve Görsel Tasarım:**
    * Tailwind CSS sınıflarını kullanarak Figma tasarımlarındaki renkleri, fontları, ikonları ve genel düzeni olabildiğince doğru bir şekilde uygulayın.
    * Listeler için düzenli bir görünüm sağlamak için Tailwind'in grid veya flexbox layout sınıflarını kullanın.

* **1.7. Arama İşlevselliği:**
    * Arama çubuğuna bir `input` alanı ekleyin.
    * `useState` kullanarak arama terimini takip edin.
    * Kullanıcı arama yaptıkça (inputun `onChange` olayında), backend'e arama terimini içeren bir istek göndererek (örn. `/api/orders?search=[terim]`) veya frontend'de mevcut sipariş listesini filtreleyerek sonuçları güncelleyin.

**2. Backend (Gerekirse)**

* Yukarıda bahsedilen `/api/dashboard/overview`, `/api/orders` gibi endpoint'lerin mevcut olup olmadığını kontrol edin. Eğer yoksa, bu endpoint'leri ilgili servisler ve controller'lar içinde geliştirin.
* Sipariş durumlarını (in progress, waiting for payment vb.) ve genel bakış istatistiklerini doğru bir şekilde hesaplayan ve döndüren iş mantığını uygulayın.

**Önemli Notlar:**

* Bu aşamada tam işlevsellik yerine öncelikle temel UI yapısını ve veri bağlantısını kurmaya odaklanın.
* Figma tasarımlarındaki tüm detaylara (ikonlar, renkler, fontlar vb.) dikkat edin.
* Kodunuzu modüler tutmaya özen gösterin (ayrı component'ler oluşturarak).
* API istekleri sırasında `loading` ve `error` durumlarını yönetmeyi unutmayın.

Bu talimatları takip ederek, uygulamanızın ana sayfası olan "Home" bölümünü Figma tasarımlarına uygun ve işlevsel bir şekilde oluşturabilirsiniz. Başarılar!

Figma URL'leri (Karanlık Tema) figma mcp (get_code, get_variable_defs, get_image, get_design_system_rules_prompt) ile verilen tüm linkleri mutlaka incele:

https://www.figma.com/design/1E7bcmE1NXSXvn8RRml6uC/Bitepoint---Restaurant-POS-App?node-id=714-72853&m=dev

https://www.figma.com/design/1E7bcmE1NXSXvn8RRml6uC/Bitepoint---Restaurant-POS-App?node-id=714-72918&m=dev

https://www.figma.com/design/1E7bcmE1NXSXvn8RRml6uC/Bitepoint---Restaurant-POS-App?node-id=714-72983&m=dev
