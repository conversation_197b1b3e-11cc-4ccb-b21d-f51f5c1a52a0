// src/notification-log/dto/update-notification-log.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateNotificationLogDto } from './create-notification-log.dto';
import {
  IsString,
  IsOptional,
  IsEnum,
  IsDateString,
} from 'class-validator';
import { NotificationStatus } from '@prisma/client';

export class UpdateNotificationLogDto extends PartialType(CreateNotificationLogDto) {
  @IsOptional()
  @IsEnum(NotificationStatus)
  status?: NotificationStatus; // Durum güncellenebilir (SENT, DELIVERED, READ, FAILED)

  @IsOptional()
  @IsDateString()
  sentAt?: Date;

  @IsOptional()
  @IsDateString()
  deliveredAt?: Date;

  @IsOptional()
  @IsDateString()
  readAt?: Date;
}
