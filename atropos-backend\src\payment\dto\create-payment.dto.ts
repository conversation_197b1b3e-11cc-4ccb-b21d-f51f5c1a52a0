// src/payment/dto/create-payment.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsInt,
  IsEnum,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentStatus } from '@prisma/client';

export class CreatePaymentDto {
  @IsString()
  @IsNotEmpty()
  orderId: string; // Hangi siparişe ait olduğu

  @IsString()
  @IsNotEmpty()
  paymentMethodId: string; // Hangi ödeme yöntemiyle yapıldığı

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0.01) // Minimum ödeme tutarı
  @IsNotEmpty()
  amount: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  tipAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  changeAmount?: number;

  @IsString()
  @IsOptional()
  approvalCode?: string;

  @IsString()
  @IsOptional()
  referenceNo?: string;

  @IsString()
  @IsOptional()
  maskedCardNumber?: string;

  @IsString()
  @IsOptional()
  cardHolderName?: string;

  @IsInt()
  @IsOptional()
  @Min(1)
  @Max(12) // Maksimum taksit sayısı (örneğin)
  installments?: number;

  @IsString()
  @IsOptional()
  transactionId?: string; // Online ödeme transaction ID'si

  // gatewayResponse Json?: DTO'da doğrudan validasyonu zor olabilir

  @IsEnum(PaymentStatus)
  @IsOptional()
  status?: PaymentStatus; // Varsayılan: PAID (eğer başarılı ise)

  // paidAt, createdAt, updatedAt, deletedAt Prisma tarafından yönetilecek
  // cashMovementId burada doğrudan alınmayacak, servis kendisi yönetecek
}
