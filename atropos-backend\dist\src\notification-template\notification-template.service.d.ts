import { PrismaService } from '../prisma/prisma.service';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationChannel } from '@prisma/client';
export declare class NotificationTemplateService {
    private prisma;
    constructor(prisma: PrismaService);
    createNotificationTemplate(data: CreateNotificationTemplateDto): Promise<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    findAllNotificationTemplates(companyId?: string, channel?: NotificationChannel, active?: boolean): Promise<({
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    })[]>;
    findOneNotificationTemplate(id: string): Promise<{
        company: {
            id: string;
            name: string;
        };
    } & {
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    updateNotificationTemplate(id: string, data: UpdateNotificationTemplateDto): Promise<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
    removeNotificationTemplate(id: string): Promise<{
        id: string;
        name: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        code: string;
        active: boolean;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        subject: string | null;
        content: string;
        smsLength: number | null;
        smsCredits: number | null;
        sendTiming: string | null;
        sendDelay: number | null;
    }>;
}
