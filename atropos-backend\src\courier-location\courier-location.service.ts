// src/courier-location/courier-location.service.ts
import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateCourierLocationDto } from './dto/create-courier-location.dto';
import { UpdateCourierLocationDto } from './dto/update-courier-location.dto';
import { UserRole } from '@prisma/client'; // UserRole enum'ını import et

@Injectable()
export class CourierLocationService {
  constructor(private prisma: PrismaService) {}

  async createCourierLocation(data: CreateCourierLocationDto) {
    // Courier (User) mevcut mu ve rolü kurye mi kontrol et
    const courier = await this.prisma.user.findUnique({
      where: { id: data.courierId, deletedAt: null },
    });
    if (!courier) {
      throw new NotFoundException(`Courier with ID "${data.courierId}" not found.`);
    }
    if (courier.role !== UserRole.COURIER && courier.role !== UserRole.ADMIN) { // Admin de kurye konumu ekleyebilir
        throw new BadRequestException(`User with ID "${data.courierId}" is not a COURIER.`);
    }

    // Branch mevcut mu kontrol et
    const branchExists = await this.prisma.branch.findUnique({
      where: { id: data.branchId, deletedAt: null },
    });
    if (!branchExists) {
      throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    const timestamp = data.timestamp ? new Date(data.timestamp) : new Date();
    // expiresAt alanını otomatik olarak belirle (örneğin 7 gün sonra)
    const expiresAt = new Date(timestamp.getTime() + (7 * 24 * 60 * 60 * 1000)); // 7 gün sonra

    return this.prisma.courierLocation.create({
      data: {
        ...data,
        timestamp: timestamp,
        expiresAt: expiresAt,
      },
    });
  }

  async findAllCourierLocations(courierId?: string, branchId?: string, startDate?: Date, endDate?: Date) {
    return this.prisma.courierLocation.findMany({
      where: {
        courierId: courierId || undefined,
        branchId: branchId || undefined,
        timestamp: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined, // Gün sonunu dahil et
        },
        expiresAt: {
            gte: new Date() // Sadece henüz süresi dolmamışları getir
        }
      },
      include: {
        courier: { select: { id: true, firstName: true, lastName: true, employeeCode: true } },
        branch: { select: { id: true, name: true } },
      },
      orderBy: { timestamp: 'desc' },
    });
  }

  async findOneCourierLocation(id: string) {
    const location = await this.prisma.courierLocation.findUnique({
      where: { id, expiresAt: { gte: new Date() } }, // Süresi dolmamış kayıtlar
      include: {
        courier: { select: { id: true, firstName: true, lastName: true, employeeCode: true } },
        branch: { select: { id: true, name: true } },
      },
    });
    if (!location) {
      throw new NotFoundException(`Courier location with ID "${id}" not found or expired.`);
    }
    return location;
  }

  async updateCourierLocation(id: string, data: UpdateCourierLocationDto) {
    // Konum kayıtları genellikle değiştirilmez (immutable).
    // Bu nedenle, latitude ve longitude gibi alanların güncellenmesine izin vermeyelim.
    // Sadece timestamp veya expiresAt gibi denetim alanlarının güncellenmesine izin verilebilir.
    const existingLocation = await this.findOneCourierLocation(id); // Süresi dolmuşsa hata fırlatacak

    if (data.courierId !== undefined || data.branchId !== undefined ||
        data.latitude !== undefined || data.longitude !== undefined) {
        throw new ForbiddenException('Cannot update courierId, branchId, latitude, or longitude of a courier location log. Create a new log instead.');
    }

    try {
      return await this.prisma.courierLocation.update({
        where: { id },
        data: {
            timestamp: data.timestamp ? new Date(data.timestamp) : undefined,
            // expiresAt'ı manuel güncellemeye izin verilmemeli, otomatik yönetilmeli.
            // data.expiresAt || undefined
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Courier location with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeCourierLocation(id: string) {
    // Konum logları genellikle fiziksel olarak silinmez, TTL ile otomatik temizlenir veya saklanır.
    // Şemanızda deletedAt alanı yok. expiresAt ile otomatik temizleme varsayılan olarak çalışacak.
    // Bu endpoint'in erişimi çok kısıtlı olmalıdır.
    try {
      return await this.prisma.courierLocation.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Courier location with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
