// src/cash-movement/dto/create-cash-movement.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsNumber,
  Min,
  IsEnum,
} from 'class-validator';
import { Type } from 'class-transformer';
import { CashMovementType } from '@prisma/client'; // Prisma'dan CashMovementType enum'ını import et

export class CreateCashMovementDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsString()
  @IsNotEmpty()
  userId: string; // Hareketi yapan kullanıcı

  @IsEnum(CashMovementType)
  @IsNotEmpty()
  type: CashMovementType; // SALE, REFUND, EXPENSE, INCOME, OPENING, CLOSING vb.

  @IsString()
  @IsOptional()
  paymentMethodId?: string; // Hangi ödeme yöntemiyle ilişkili olduğu (örn: Cash Sale)

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsNotEmpty()
  amount: number; // Pozitif veya negatif olabilir (gelir/gider)

  @IsString()
  @IsNotEmpty()
  description: string;

  @IsString()
  @IsOptional()
  referenceId?: string; // Order ID, Expense ID vb.

  @IsString()
  @IsOptional()
  referenceType?: string; // "ORDER", "EXPENSE", "DAILY_REPORT" vb.

  // previousBalance ve currentBalance servis tarafından hesaplanacak

  @IsString()
  @IsOptional()
  cashRegisterId?: string; // ÖKC cihaz no veya kasa adı

  @IsString()
  @IsOptional()
  safeId?: string; // Güvenli kasa ID'si (örn: ana kasa)

  @IsString()
  @IsOptional()
  approvedBy?: string; // Onaylayan kullanıcı ID'si

  // approvedAt DateTime? servis tarafından ayarlanacak
}
