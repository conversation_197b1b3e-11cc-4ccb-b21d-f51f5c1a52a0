// src/stock-movement/stock-movement.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { StockMovementService } from './stock-movement.service';
import { CreateStockMovementDto } from './dto/create-stock-movement.dto';
import { UpdateStockMovementDto } from './dto/update-stock-movement.dto';
import { StockMovementType } from '@prisma/client';
// import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı
// import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe'; // Enum Pipe'ı

@Controller('stock-movement')
export class StockMovementController {
  constructor(private readonly stockMovementService: StockMovementService) {}

  @Post() // POST /stock-movement
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createStockMovementDto: CreateStockMovementDto) {
    return this.stockMovementService.createStockMovement(createStockMovementDto);
  }

  @Get() // GET /stock-movement?branchId=...&productId=...&inventoryItemId=...&type=...&startDate=...&endDate=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('productId') productId?: string,
    @Query('inventoryItemId') inventoryItemId?: string,
    @Query('type') type?: StockMovementType,
    @Query('startDate') startDate?: string,
    @Query('endDate') endDate?: string,
  ) {
    return this.stockMovementService.findAllStockMovements(
      branchId,
      productId,
      inventoryItemId,
      type as StockMovementType,
      startDate ? new Date(startDate) : undefined,
      endDate ? new Date(endDate) : undefined,
    );
  }

  @Get(':id') // GET /stock-movement/:id
  findOne(@Param('id') id: string) {
    return this.stockMovementService.findOneStockMovement(id);
  }

  @Patch(':id') // PATCH /stock-movement/:id
  update(@Param('id') id: string, @Body() updateStockMovementDto: UpdateStockMovementDto) {
    return this.stockMovementService.updateStockMovement(id, updateStockMovementDto);
  }

  @Delete(':id') // DELETE /stock-movement/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.stockMovementService.removeStockMovement(id);
  }
}
