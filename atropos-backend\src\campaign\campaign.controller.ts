// src/campaign/campaign.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  ParseBoolPipe, // boolean query parametreleri için
} from '@nestjs/common';
import { CampaignService } from './campaign.service';
import { CreateCampaignDto } from './dto/create-campaign.dto';
import { UpdateCampaignDto } from './dto/update-campaign.dto';
import { CampaignType } from '@prisma/client';
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe';

@Controller('campaign')
export class CampaignController {
  constructor(private readonly campaignService: CampaignService) {}

  @Post() // POST /campaign
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createCampaignDto: CreateCampaignDto) {
    return this.campaignService.createCampaign(createCampaignDto);
  }

  @Get() // GET /campaign?companyId=...&campaignType=...&active=...
  findAll(
    @Query('companyId') companyId?: string,
    @Query('campaignType', new ParseOptionalEnumPipe(CampaignType)) campaignType?: CampaignType,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean, // boolean sorgu parametresi
  ) {
    return this.campaignService.findAllCampaigns(companyId, campaignType, active);
  }

  @Get(':id') // GET /campaign/:id
  findOne(@Param('id') id: string) {
    return this.campaignService.findOneCampaign(id);
  }

  @Patch(':id') // PATCH /campaign/:id
  update(@Param('id') id: string, @Body() updateCampaignDto: UpdateCampaignDto) {
    return this.campaignService.updateCampaign(id, updateCampaignDto);
  }

  @Delete(':id') // DELETE /campaign/:id (Pasif hale getir)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.campaignService.removeCampaign(id);
  }
}
