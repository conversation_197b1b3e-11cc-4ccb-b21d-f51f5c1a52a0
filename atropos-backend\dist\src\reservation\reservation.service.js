"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.ReservationService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let ReservationService = class ReservationService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createReservation(data) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        if (data.customerId) {
            const customerExists = await this.prisma.customer.findUnique({ where: { id: data.customerId, deletedAt: null } });
            if (!customerExists) {
                throw new common_1.NotFoundException(`Customer with ID "${data.customerId}" not found.`);
            }
        }
        if (data.createdBy) {
            const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
            if (!createdByExists) {
                throw new common_1.NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`);
            }
        }
        if (data.confirmedBy) {
            const confirmedByExists = await this.prisma.user.findUnique({ where: { id: data.confirmedBy, deletedAt: null } });
            if (!confirmedByExists) {
                throw new common_1.NotFoundException(`User (confirmedBy) with ID "${data.confirmedBy}" not found.`);
            }
        }
        if (data.tableIds && data.tableIds.length > 0) {
            const tables = await this.prisma.table.findMany({
                where: { id: { in: data.tableIds }, branchId: data.branchId, deletedAt: null },
            });
            if (tables.length !== data.tableIds.length) {
                const foundIds = tables.map(t => t.id);
                const notFoundIds = data.tableIds.filter(id => !foundIds.includes(id));
                throw new common_1.NotFoundException(`Some tables not found or not in specified branch: ${notFoundIds.join(', ')}`);
            }
            const occupiedTables = tables.filter(t => t.status !== 'EMPTY' && t.status !== 'CLEANING');
            if (occupiedTables.length > 0) {
                throw new common_1.ConflictException(`Some selected tables are not available: ${occupiedTables.map(t => t.number).join(', ')}. Status: ${occupiedTables.map(t => t.status).join(', ')}`);
            }
            const totalCapacity = tables.reduce((sum, t) => sum + t.capacity, 0);
            if (data.guestCount > totalCapacity) {
                throw new common_1.BadRequestException(`Selected tables' total capacity (${totalCapacity}) is insufficient for ${data.guestCount} guests.`);
            }
        }
        const resDate = new Date(data.reservationDate);
        resDate.setUTCHours(0, 0, 0, 0);
        const [hours, minutes] = data.reservationTime.split(':').map(Number);
        const reservationStartDateTime = new Date(resDate.getFullYear(), resDate.getMonth(), resDate.getDate(), hours, minutes, 0, 0);
        const reservationEndDateTime = new Date(reservationStartDateTime.getTime() + (data.duration || 120) * 60 * 1000);
        const overlappingReservations = await this.prisma.reservation.findMany({
            where: {
                branchId: data.branchId,
                status: { notIn: ['CANCELLED', 'NO_SHOW', 'COMPLETED'] },
                reservationDate: resDate,
                tableIds: { hasSome: data.tableIds || [] }
            },
        });
        if (overlappingReservations.length > 0) {
            let hasTableConflict = false;
            if (data.tableIds && data.tableIds.length > 0) {
                for (const existingRes of overlappingReservations) {
                    const existingResTables = new Set(existingRes.tableIds);
                    for (const newTableId of data.tableIds) {
                        if (existingResTables.has(newTableId)) {
                            hasTableConflict = true;
                            break;
                        }
                    }
                    if (hasTableConflict)
                        break;
                }
            }
            if (hasTableConflict) {
                throw new common_1.ConflictException('Selected tables are already reserved for this time slot.');
            }
        }
        const reservation = await this.prisma.reservation.create({
            data: {
                branchId: data.branchId,
                customerId: data.customerId,
                customerName: data.customerName,
                customerPhone: data.phone,
                customerEmail: data.customerEmail,
                reservationDate: resDate,
                reservationTime: data.reservationTime,
                reservationStartDateTime,
                reservationEndDateTime,
                duration: data.duration || 120,
                guestCount: data.guestCount,
                childCount: data.childCount || 0,
                tableIds: data.tableIds || [],
                tablePreference: data.tablePreference,
                status: data.status || client_1.ReservationStatus.PENDING,
                specialRequests: data.specialRequests,
                allergyInfo: data.allergyInfo,
                occasionType: data.occasionType,
                internalNotes: data.internalNotes,
                source: data.source || 'PHONE',
                confirmationCode: data.confirmationCode,
                confirmedBy: data.confirmedBy,
                depositRequired: data.depositRequired || false,
                depositAmount: data.depositAmount !== undefined ? parseFloat(data.depositAmount.toFixed(2)) : undefined,
                depositPaid: data.depositPaid || false,
                cancelReason: data.cancelReason,
                noShowFee: data.noShowFee !== undefined ? parseFloat(data.noShowFee.toFixed(2)) : undefined,
                createdBy: data.createdBy,
            },
            include: { customer: true, branch: true, },
        });
        if (data.tableIds && data.tableIds.length > 0) {
            await this.prisma.table.updateMany({
                where: { id: { in: data.tableIds } },
                data: { status: 'RESERVED' },
            });
        }
        return reservation;
    }
    async findAllReservations(branchId, customerId, status, startDate, endDate) {
        return this.prisma.reservation.findMany({
            where: {
                branchId: branchId || undefined,
                customerId: customerId || undefined,
                status: status || undefined,
                reservationDate: {
                    gte: startDate ? new Date(startDate.toISOString().split('T')[0]) : undefined,
                    lte: endDate ? new Date(endDate.toISOString().split('T')[0] + 'T23:59:59.999Z') : undefined,
                },
            },
            include: {
                branch: { select: { id: true, name: true } },
                customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
            },
            orderBy: [{ reservationDate: 'asc' }, { reservationTime: 'asc' }],
        });
    }
    async findOneReservation(id) {
        const reservation = await this.prisma.reservation.findUnique({
            where: { id },
            include: {
                branch: { select: { id: true, name: true } },
                customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
            },
        });
        if (!reservation) {
            throw new common_1.NotFoundException(`Reservation with ID "${id}" not found.`);
        }
        return reservation;
    }
    async updateReservation(id, data) {
        const existingReservation = await this.findOneReservation(id);
        if (existingReservation.status === client_1.ReservationStatus.CANCELLED || existingReservation.status === client_1.ReservationStatus.COMPLETED || existingReservation.status === client_1.ReservationStatus.NO_SHOW) {
            throw new common_1.BadRequestException(`Cannot update a reservation with status "${existingReservation.status}".`);
        }
        if (data.branchId && data.branchId !== existingReservation.branchId) {
            const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
            if (!branchExists)
                throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        if (data.tableIds && data.tableIds.length > 0) {
            const tables = await this.prisma.table.findMany({
                where: { id: { in: data.tableIds }, branchId: data.branchId || existingReservation.branchId, deletedAt: null },
            });
            if (tables.length !== data.tableIds.length) {
                const foundIds = tables.map(t => t.id);
                const notFoundIds = data.tableIds.filter(id => !foundIds.includes(id));
                throw new common_1.NotFoundException(`Some tables not found or not in specified branch: ${notFoundIds.join(', ')}`);
            }
            const occupiedTables = tables.filter(t => t.status !== 'EMPTY' && t.status !== 'CLEANING' && !existingReservation.tableIds.includes(t.id));
            if (occupiedTables.length > 0) {
                throw new common_1.ConflictException(`Some selected tables are not available: ${occupiedTables.map(t => t.number).join(', ')}. Status: ${occupiedTables.map(t => t.status).join(', ')}`);
            }
            const totalCapacity = tables.reduce((sum, t) => sum + t.capacity, 0);
            if (data.guestCount && data.guestCount > totalCapacity) {
                throw new common_1.BadRequestException(`Selected tables' total capacity (${totalCapacity}) is insufficient for ${data.guestCount} guests.`);
            }
            const oldTablesToEmpty = existingReservation.tableIds.filter(oldId => !(data.tableIds || []).includes(oldId));
            const newTablesToReserve = (data.tableIds || []).filter(newId => !existingReservation.tableIds.includes(newId));
            if (oldTablesToEmpty.length > 0) {
                await this.prisma.table.updateMany({
                    where: { id: { in: oldTablesToEmpty } },
                    data: { status: 'EMPTY' },
                });
            }
            if (newTablesToReserve.length > 0) {
                await this.prisma.table.updateMany({
                    where: { id: { in: newTablesToReserve } },
                    data: { status: 'RESERVED' },
                });
            }
        }
        if (data.reservationDate || data.reservationTime || data.duration || data.tableIds) {
            const newReservationDate = data.reservationDate || existingReservation.reservationDate;
            const newTableIds = data.tableIds || existingReservation.tableIds;
            const overlappingReservations = await this.prisma.reservation.findMany({
                where: {
                    branchId: data.branchId || existingReservation.branchId,
                    id: { not: id },
                    status: { notIn: ['CANCELLED', 'NO_SHOW', 'COMPLETED'] },
                    reservationDate: newReservationDate,
                    tableIds: { hasSome: newTableIds || [] }
                },
            });
            if (overlappingReservations.length > 0) {
                let hasTableConflict = false;
                if (newTableIds && newTableIds.length > 0) {
                    for (const existingRes of overlappingReservations) {
                        const existingResTables = new Set(existingRes.tableIds);
                        for (const newTableId of newTableIds) {
                            if (existingResTables.has(newTableId)) {
                                hasTableConflict = true;
                                break;
                            }
                        }
                        if (hasTableConflict)
                            break;
                    }
                }
                if (hasTableConflict) {
                    throw new common_1.ConflictException('Selected tables are already reserved for this updated time slot.');
                }
            }
        }
        try {
            const updatedReservation = await this.prisma.reservation.update({
                where: { id },
                data: {
                    branchId: data.branchId,
                    customerId: data.customerId,
                    customerName: data.customerName,
                    customerPhone: data.phone,
                    customerEmail: data.customerEmail,
                    reservationDate: data.reservationDate,
                    reservationTime: data.reservationTime,
                    duration: data.duration,
                    guestCount: data.guestCount,
                    childCount: data.childCount,
                    tableIds: data.tableIds,
                    tablePreference: data.tablePreference,
                    status: data.status,
                    specialRequests: data.specialRequests,
                    allergyInfo: data.allergyInfo,
                    occasionType: data.occasionType,
                    internalNotes: data.internalNotes,
                    source: data.source,
                    confirmationCode: data.confirmationCode,
                    confirmedBy: data.confirmedBy,
                    depositRequired: data.depositRequired,
                    depositAmount: data.depositAmount !== undefined ? parseFloat(data.depositAmount.toFixed(2)) : undefined,
                    depositPaid: data.depositPaid,
                    reminderSent: data.reminderSent,
                    reminderSentAt: data.reminderSentAt,
                    confirmedAt: data.confirmedAt,
                    cancelledAt: data.cancelledAt,
                    seatedAt: data.seatedAt,
                    completedAt: data.completedAt,
                    cancelReason: data.cancelReason,
                    noShowFee: data.noShowFee !== undefined ? parseFloat(data.noShowFee.toFixed(2)) : undefined,
                    createdBy: data.createdBy,
                },
            });
            if (data.status && updatedReservation.tableIds && updatedReservation.tableIds.length > 0) {
                let newTableStatus;
                switch (data.status) {
                    case client_1.ReservationStatus.SEATED:
                        newTableStatus = 'OCCUPIED';
                        break;
                    case client_1.ReservationStatus.CANCELLED:
                    case client_1.ReservationStatus.NO_SHOW:
                    case client_1.ReservationStatus.COMPLETED:
                        newTableStatus = 'EMPTY';
                        break;
                    default: break;
                }
                if (newTableStatus) {
                    await this.prisma.table.updateMany({
                        where: { id: { in: updatedReservation.tableIds } },
                        data: { status: newTableStatus },
                    });
                }
            }
            return updatedReservation;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Reservation with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeReservation(id) {
        try {
            const reservation = await this.prisma.reservation.update({
                where: { id },
                data: { status: 'CANCELLED', cancelledAt: new Date(), cancelReason: 'Deleted by admin.' },
            });
            if (reservation.tableIds && reservation.tableIds.length > 0) {
                await this.prisma.table.updateMany({
                    where: { id: { in: reservation.tableIds } },
                    data: { status: 'EMPTY' },
                });
            }
            return reservation;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Reservation with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.ReservationService = ReservationService;
exports.ReservationService = ReservationService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], ReservationService);
//# sourceMappingURL=reservation.service.js.map