import { TaskService } from './task.service';
import { CreateTaskDto } from './dto/create-task.dto';
import { UpdateTaskDto } from './dto/update-task.dto';
import { $Enums } from '@prisma/client';
export declare class TaskController {
    private readonly taskService;
    constructor(taskService: TaskService);
    create(createTaskDto: CreateTaskDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
    findAll(companyId?: string, branchId?: string, assignedToId?: string, status?: $Enums.TaskStatus, priority?: $Enums.TaskPriority, startDate?: Date, endDate?: Date): Promise<({
        company: {
            id: string;
            name: string;
        };
        branch: {
            id: string;
            name: string;
        } | null;
        assignedTo: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        createdByUser: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    })[]>;
    findOne(id: string): Promise<{
        company: {
            id: string;
            name: string;
        };
        branch: {
            id: string;
            name: string;
        } | null;
        assignedTo: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        } | null;
        createdByUser: {
            id: string;
            username: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
    update(id: string, updateTaskDto: UpdateTaskDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        companyId: string;
        branchId: string | null;
        description: string | null;
        status: $Enums.TaskStatus;
        completedAt: Date | null;
        title: string;
        createdBy: string;
        assignedToId: string | null;
        priority: $Enums.TaskPriority;
        dueDate: Date | null;
    }>;
}
