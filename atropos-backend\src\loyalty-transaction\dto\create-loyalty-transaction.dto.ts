// src/loyalty-transaction/dto/create-loyalty-transaction.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  IsEnum,
  IsNumber,
  IsDateString,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LoyaltyTransactionType } from '@prisma/client'; // Enum'ı import et

export class CreateLoyaltyTransactionDto {
  @IsString()
  @IsNotEmpty()
  cardId: string; // Hangi sadakat kartına ait olduğu

  @IsString()
  @IsOptional()
  orderId?: string; // Hangi siparişle ilişkili olduğu (örn: EARN_PURCHASE için)

  @IsEnum(LoyaltyTransactionType)
  @IsNotEmpty()
  type: LoyaltyTransactionType; // EARN_PURCHASE, SPEND_DISCOUNT, LOAD_BALANCE vb.

  @IsInt()
  @IsOptional()
  points?: number; // Puan hareketi (+ veya -)

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @IsOptional()
  amount?: number; // Para hareketi (+ veya -)

  @IsString()
  @IsNotEmpty()
  description: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  baseAmount?: number; // Puan kazanılan tutar (EARN_PURCHASE için)

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  multiplier?: number; // Kampanya çarpanı

  @IsOptional()
  @IsDateString()
  expiresAt?: Date; // Puan son kullanma tarihi

  @IsString()
  @IsOptional()
  createdBy?: string; // İşlemi yapan kullanıcı ID'si
}
