// src/order/order.service.ts
import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateOrderDto, CreateOrderItemDto } from './dto/create-order.dto';
import { UpdateOrderDto, UpdateOrderItemDto } from './dto/update-order.dto';
import { OrderStatus, PaymentStatus, OrderType } from '@prisma/client';

@Injectable()
export class OrderService {
  constructor(private prisma: PrismaService) {}

  private async calculateOrderTotals(items: (CreateOrderItemDto | UpdateOrderItemDto)[]) {
    let subtotal = 0;
    let taxAmount = 0;
    let totalAmount = 0;

    for (const item of items) {
      if (!item.unitPrice || !item.quantity || !item.taxRate) continue;

      const itemPrice = item.unitPrice * item.quantity;
      const itemDiscount = item.discountAmount || (item.discountRate ? (itemPrice * item.discountRate) / 100 : 0);
      const itemSubtotal = itemPrice - itemDiscount;
      const itemTax = (itemSubtotal * item.taxRate) / 100;
      const itemTotal = itemSubtotal + itemTax;

      subtotal += itemSubtotal;
      taxAmount += itemTax;
      totalAmount += itemTotal;
    }

    return {
      subtotal: parseFloat(subtotal.toFixed(2)),
      taxAmount: parseFloat(taxAmount.toFixed(2)),
      totalAmount: parseFloat(totalAmount.toFixed(2)),
    };
  }

  async createOrder(data: CreateOrderDto) {
    // İlişkili varlıkların varlığını kontrol et
    const branchExists = await this.prisma.branch.findUnique({
      where: { id: data.branchId, deletedAt: null },
    });
    if (!branchExists) {
      throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    if (data.tableId) {
      const tableExists = await this.prisma.table.findUnique({
        where: { id: data.tableId, deletedAt: null },
      });
      if (!tableExists) {
        throw new NotFoundException(`Table with ID "${data.tableId}" not found.`);
      }
      // Masa durumunu güncelleyebiliriz (örn: OCCUPIED)
      await this.prisma.table.update({
        where: { id: data.tableId },
        data: { status: 'OCCUPIED' },
      });
    }

    if (data.customerId) {
      const customerExists = await this.prisma.customer.findUnique({
        where: { id: data.customerId, deletedAt: null },
      });
      if (!customerExists) {
        throw new NotFoundException(`Customer with ID "${data.customerId}" not found.`);
      }
    }

    if (data.waiterId) {
      const waiterExists = await this.prisma.user.findUnique({
        where: { id: data.waiterId, deletedAt: null, role: { in: ['WAITER', 'ADMIN', 'BRANCH_MANAGER'] } },
      });
      if (!waiterExists) {
        throw new NotFoundException(`Waiter with ID "${data.waiterId}" not found or unauthorized.`);
      }
    }
    
    // Sipariş kalemlerini doğrula ve fiyatları hesapla
    for (const item of data.items) {
      const product = await this.prisma.product.findUnique({
        where: { id: item.productId, deletedAt: null },
        include: { tax: true, variants: true }, // Tax ve varyantları da çek
      });
      if (!product || !product.sellable) {
        throw new NotFoundException(`Product with ID "${item.productId}" not found or not sellable.`);
      }
      if (item.variantId) {
        const variant = product.variants.find(v => v.id === item.variantId);
        if (!variant) {
          throw new NotFoundException(`Variant with ID "${item.variantId}" not found for product "${item.productId}".`);
        }
        item.unitPrice = variant.price.toNumber(); // Variant fiyatını kullan
        item.costPrice = variant.costPrice?.toNumber(); // Variant maliyetini kullan
      } else {
        item.unitPrice = product.basePrice.toNumber(); // Ürün fiyatını kullan
        item.costPrice = product.costPrice?.toNumber(); // Ürün maliyetini kullan
      }
      item.taxRate = product.tax.rate.toNumber(); // Ürünün vergi oranını kullan

      // TotalAmount otomatik hesaplanmalı, DTO'da almayacağız
      // item.totalAmount = (item.unitPrice * item.quantity) * (1 + item.taxRate / 100); // Basit hesap
    }

    // Toplamları hesapla
    const { subtotal, taxAmount, totalAmount } = await this.calculateOrderTotals(data.items);

    // Yeni bir orderNumber oluştur (örnek: YYYY-XXXXXX)
    const currentYear = new Date().getFullYear();
    const latestOrder = await this.prisma.order.findFirst({
        where: { branchId: data.branchId, createdAt: { gte: new Date(`${currentYear}-01-01T00:00:00Z`) } }, // Bu yılki siparişler
        orderBy: { createdAt: 'desc' }
    });
    let orderNumber = `${currentYear}-000001`;
    if (latestOrder && latestOrder.orderNumber) {
        const lastNumber = parseInt(latestOrder.orderNumber.split('-')[1]);
        orderNumber = `${currentYear}-${String(lastNumber + 1).padStart(6, '0')}`;
    }


    const order = await this.prisma.order.create({
      data: {
        ...data,
        orderNumber, // Otomatik oluşturulan orderNumber
        status: OrderStatus.PENDING, // Başlangıç durumu
        paymentStatus: 'UNPAID', // Başlangıç ödeme durumu
        subtotal,
        taxAmount,
        totalAmount,
        items: {
          create: data.items.map(item => ({
            productId: item.productId,
            variantId: item.variantId,
            quantity: item.quantity,
            unitPrice: item.unitPrice,
            costPrice: item.costPrice,
            discountAmount: item.discountAmount || 0,
            discountRate: item.discountRate || 0,
            taxRate: item.taxRate,
            taxAmount: (item.unitPrice * item.quantity * item.taxRate / 100), // Her kalem için vergi
            totalAmount: (item.unitPrice * item.quantity) - (item.discountAmount || (item.discountRate ? (item.unitPrice * item.discountRate) / 100 : 0)) * (1 + item.taxRate / 100), // Her kalem için toplam
            note: item.note,
            guestName: item.guestName,
            courseNumber: item.courseNumber,
          })),
        },
      },
      include: { items: true }, // Oluşturulan siparişi ve kalemlerini dön
    });

    return order;
  }

  async findAllOrders(
    branchId?: string,
    tableId?: string,
    customerId?: string,
    status?: OrderStatus,
    paymentStatus?: PaymentStatus,
    orderType?: OrderType,
    startDate?: Date,
    endDate?: Date,
  ) {
    return this.prisma.order.findMany({
      where: {
        branchId: branchId || undefined,
        tableId: tableId || undefined,
        customerId: customerId || undefined,
        status: status || undefined,
        paymentStatus: paymentStatus || undefined,
        orderType: orderType || undefined,
        orderedAt: {
          gte: startDate || undefined,
          lte: endDate || undefined,
        },
        deletedAt: null,
      },
      include: {
        branch: { select: { id: true, name: true } },
        table: { select: { id: true, number: true } },
        customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
        waiter: { select: { id: true, firstName: true, lastName: true } },
        items: {
            include: { product: true, variant: true } // Sipariş kalemlerinin ürün ve varyantlarını da getir
        }
      },
      orderBy: { orderedAt: 'desc' },
    });
  }

  async findOneOrder(id: string) {
    const order = await this.prisma.order.findUnique({
      where: { id, deletedAt: null },
      include: {
        branch: { select: { id: true, name: true } },
        table: { select: { id: true, number: true } },
        customer: { select: { id: true, firstName: true, lastName: true, phone: true } },
        waiter: { select: { id: true, firstName: true, lastName: true } },
        // cashier: { select: { id: true, firstName: true, lastName: true } },
        courier: { select: { id: true, firstName: true, lastName: true } },
        onlinePlatform: { select: { id: true, name: true } },
        items: {
            include: { product: true, variant: true } // Sipariş kalemlerinin ürün ve varyantlarını da getir
        },
        payments: true,
      },
    });
    if (!order) {
      throw new NotFoundException(`Order with ID "${id}" not found.`);
    }
    return order;
  }

  async updateOrder(id: string, data: UpdateOrderDto) {
    const existingOrder = await this.findOneOrder(id);

    // İlişkili varlıkların güncelliğini kontrol et
    if (data.branchId && data.branchId !== existingOrder.branchId) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) throw new NotFoundException(`Branch with ID "${data.branchId}" not found.`);
    }

    let updatedOrderData: any = { ...data };
    let newItems: CreateOrderItemDto[] = [];
    let itemsToUpdate: UpdateOrderItemDto[] = [];
    let itemIdsToDelete: string[] = [];

    // Sipariş kalemleri yönetimi (ekleme, güncelleme, silme)
    if (data.items) {
      for (const item of data.items) {
        if (item.id) { // Mevcut kalem ise güncelle
            itemsToUpdate.push(item);
        } else { // Yeni kalem ise ekle
            newItems.push(item as CreateOrderItemDto);
        }
      }

      // Veritabanındaki mevcut kalemler
      const currentItems = await this.prisma.orderItem.findMany({ where: { orderId: id } });
      const currentItemIds = currentItems.map(i => i.id);
      const updatedItemIds = itemsToUpdate.map(i => i.id);

      // Silinecek kalemleri belirle (gelen listede olmayanlar)
      itemIdsToDelete = currentItemIds.filter(itemId => !updatedItemIds.includes(itemId));
    }

    // Hesaplamaları tekrar yap (yalnızca kalemler güncelleniyorsa)
    if (data.items) {
        // Combine original items (excluding deleted ones) with new/updated items
        const originalItems = await this.prisma.orderItem.findMany({ where: { orderId: id, id: { notIn: itemIdsToDelete } } });
        const processedItems = new Map<string, any>(); // Map to handle updates and new items

        originalItems.forEach(item => {
            // Convert Prisma.Decimal to number for calculations
            processedItems.set(item.id, {
                ...item,
                quantity: item.quantity.toNumber(),
                unitPrice: item.unitPrice.toNumber(),
                discountAmount: item.discountAmount.toNumber(),
                discountRate: item.discountRate.toNumber(),
                taxRate: item.taxRate.toNumber(),
                costPrice: item.costPrice?.toNumber() // Convert if exists
            });
        });

        itemsToUpdate.forEach(item => {
            if (!item.id) return;
            const existing = processedItems.get(item.id);
            if (existing) {
                processedItems.set(item.id, {
                    ...existing,
                    ...item,
                    quantity: item.quantity !== undefined ? parseFloat(item.quantity.toFixed(3)) : existing.quantity,
                    unitPrice: item.unitPrice !== undefined ? parseFloat(item.unitPrice.toFixed(2)) : existing.unitPrice,
                    discountAmount: item.discountAmount !== undefined ? parseFloat(item.discountAmount.toFixed(2)) : existing.discountAmount,
                    discountRate: item.discountRate !== undefined ? parseFloat(item.discountRate.toFixed(2)) : existing.discountRate,
                    taxRate: item.taxRate !== undefined ? parseFloat(item.taxRate.toFixed(2)) : existing.taxRate,
                    costPrice: item.costPrice !== undefined ? parseFloat(item.costPrice.toFixed(2)) : existing.costPrice,
                });
            }
        });

        newItems.forEach(item => {
            processedItems.set(item.productId + (item.variantId || ''), item); // Unique key
        });

        const { subtotal, taxAmount, totalAmount } = await this.calculateOrderTotals(Array.from(processedItems.values()));
        updatedOrderData.subtotal = subtotal;
        updatedOrderData.taxAmount = taxAmount;
        updatedOrderData.totalAmount = totalAmount;
    }

    // Sipariş kalemi işlemlerini gerçekleştir
    const transaction: any[] = [];

    // Mevcut kalemleri güncelle
    for (const item of itemsToUpdate) {
        if (!item.id) continue;
        transaction.push(
            this.prisma.orderItem.update({
                where: { id: item.id },
                data: {
                    productId: item.productId,
                    variantId: item.variantId,
                    quantity: item.quantity !== undefined ? parseFloat(item.quantity.toFixed(3)) : undefined,
                    unitPrice: item.unitPrice !== undefined ? parseFloat(item.unitPrice.toFixed(2)) : undefined,
                    costPrice: item.costPrice !== undefined ? parseFloat(item.costPrice.toFixed(2)) : undefined,
                    discountAmount: item.discountAmount !== undefined ? parseFloat(item.discountAmount.toFixed(2)) : undefined,
                    discountRate: item.discountRate !== undefined ? parseFloat(item.discountRate.toFixed(2)) : undefined,
                    taxRate: item.taxRate !== undefined ? parseFloat(item.taxRate.toFixed(2)) : undefined,
                    // Recalculate item totals
                    taxAmount: item.unitPrice && item.quantity && item.taxRate ? (item.unitPrice * item.quantity * item.taxRate / 100) : undefined,
                    totalAmount: item.unitPrice && item.quantity && item.taxRate ? ((item.unitPrice * item.quantity) - (item.discountAmount || (item.discountRate ? (item.unitPrice * item.discountRate) / 100 : 0))) * (1 + item.taxRate / 100) : undefined,
                    note: item.note,
                    guestName: item.guestName,
                    courseNumber: item.courseNumber,
                }
            })
        );
    }

    // Yeni kalemleri ekle
    if (newItems.length > 0) {
        transaction.push(
            this.prisma.orderItem.createMany({
                data: newItems.map(item => ({
                    orderId: id,
                    productId: item.productId,
                    variantId: item.variantId,
                    quantity: parseFloat(item.quantity.toFixed(3)),
                    unitPrice: parseFloat(item.unitPrice.toFixed(2)),
                    costPrice: item.costPrice !== undefined ? parseFloat(item.costPrice.toFixed(2)) : undefined,
                    discountAmount: item.discountAmount !== undefined ? parseFloat(item.discountAmount.toFixed(2)) : 0,
                    discountRate: item.discountRate !== undefined ? parseFloat(item.discountRate.toFixed(2)) : 0,
                    taxRate: parseFloat(item.taxRate.toFixed(2)),
                    taxAmount: (item.unitPrice * item.quantity * item.taxRate / 100),
                    totalAmount: ((item.unitPrice * item.quantity) - (item.discountAmount || (item.discountRate ? (item.unitPrice * item.discountRate) / 100 : 0))) * (1 + item.taxRate / 100),
                    note: item.note,
                    guestName: item.guestName,
                    courseNumber: item.courseNumber,
                }))
            })
        );
    }

    // Silinecek kalemleri sil
    if (itemIdsToDelete.length > 0) {
        transaction.push(
            this.prisma.orderItem.deleteMany({
                where: { id: { in: itemIdsToDelete } }
            })
        );
    }

    // Tüm transaction'ları tek bir işlemde çalıştır
    if (transaction.length > 0) {
        await this.prisma.$transaction(transaction);
    }

    // Masa durumunu güncelle (Eğer sipariş durumu tamamlandı/iptal edildi ise masayı boşalt)
    if (data.status && ['COMPLETED', 'CANCELLED', 'RETURNED'].includes(data.status) && existingOrder.tableId) {
        await this.prisma.table.update({
            where: { id: existingOrder.tableId },
            data: { status: 'EMPTY' },
        });
    }

    try {
      return await this.prisma.order.update({
        where: { id, deletedAt: null },
        data: updatedOrderData,
        include: { items: true },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Order with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeOrder(id: string) {
    // Soft delete uygulaması
    try {
      const order = await this.prisma.order.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), status: 'CANCELLED' },
      });

      // Eğer masa bağlantılı ise masayı boşalt
      if (order.tableId) {
        await this.prisma.table.update({
          where: { id: order.tableId },
          data: { status: 'EMPTY' },
        });
      }
      return order;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Order with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
