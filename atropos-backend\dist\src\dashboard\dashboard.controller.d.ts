import { DashboardService } from './dashboard.service';
export declare class DashboardController {
    private readonly dashboardService;
    constructor(dashboardService: DashboardService);
    getOverview(branchId?: string): Promise<{
        totalEarning: number;
        totalEarningChange: number;
        inProgress: number;
        inProgressChange: number;
        waitingList: number;
        waitingListChange: number;
    }>;
}
