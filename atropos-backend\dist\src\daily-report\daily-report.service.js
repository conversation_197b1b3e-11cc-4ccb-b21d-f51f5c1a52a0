"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DailyReportService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let DailyReportService = class DailyReportService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async generateReportData(branchId, reportDate) {
        const startOfDay = new Date(reportDate);
        startOfDay.setHours(0, 0, 0, 0);
        const endOfDay = new Date(reportDate);
        endOfDay.setHours(23, 59, 59, 999);
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: branchId, deletedAt: null }
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${branchId}" not found.`);
        }
        const orders = await this.prisma.order.findMany({
            where: {
                branchId,
                orderedAt: { gte: startOfDay, lte: endOfDay },
                deletedAt: null,
            },
            include: {
                items: {
                    include: { product: { include: { tax: true, category: true } } },
                },
                payments: {
                    where: { deletedAt: null, status: { in: ['PAID', 'PARTIALLY_PAID', 'PARTIALLY_REFUNDED', 'REFUNDED'] } },
                    include: { paymentMethod: true }
                }
            },
        });
        const cashMovements = await this.prisma.cashMovement.findMany({
            where: {
                branchId,
                createdAt: { gte: startOfDay, lte: endOfDay }
            }
        });
        let totalOrders = orders.length;
        let totalItems = 0;
        let totalCustomers = 0;
        let grossSales = 0;
        let totalDiscount = 0;
        let totalServiceCharge = 0;
        let netSales = 0;
        let totalTax = 0;
        let totalSales = 0;
        let cashSales = 0;
        let creditCardSales = 0;
        let debitCardSales = 0;
        let mealCardSales = 0;
        let otherSales = 0;
        let totalReturns = 0;
        let totalCancellations = 0;
        const customerIds = new Set();
        const taxBreakdown = {};
        const categoryBreakdown = {};
        const hourlyBreakdown = {};
        for (const order of orders) {
            totalItems += order.items.reduce((sum, item) => sum + item.quantity.toNumber(), 0);
            if (order.customerId) {
                customerIds.add(order.customerId);
            }
            if (order.status === 'CANCELLED') {
                totalCancellations += order.totalAmount.toNumber();
                continue;
            }
            if (order.status === 'RETURNED') {
                totalReturns += order.totalAmount.toNumber();
                continue;
            }
            grossSales += order.subtotal.toNumber();
            totalDiscount += order.discountAmount.toNumber();
            totalServiceCharge += order.serviceCharge.toNumber();
            totalSales += order.totalAmount.toNumber();
            totalTax += order.taxAmount.toNumber();
            for (const payment of order.payments) {
                if (['PAID', 'PARTIALLY_PAID'].includes(payment.status)) {
                    switch (payment.paymentMethod.type) {
                        case 'CASH':
                            cashSales += payment.amount.toNumber();
                            break;
                        case 'CREDIT_CARD':
                            creditCardSales += payment.amount.toNumber();
                            break;
                        case 'DEBIT_CARD':
                            debitCardSales += payment.amount.toNumber();
                            break;
                        case 'MEAL_CARD':
                            mealCardSales += payment.amount.toNumber();
                            break;
                        default:
                            otherSales += payment.amount.toNumber();
                            break;
                    }
                }
                else if (['PARTIALLY_REFUNDED', 'REFUNDED'].includes(payment.status)) {
                    totalReturns += payment.refundAmount?.toNumber() || 0;
                }
            }
            for (const item of order.items) {
                const itemSubtotal = (item.unitPrice.toNumber() * item.quantity.toNumber()) - (item.discountAmount.toNumber() || (item.discountRate.toNumber() ? (item.unitPrice.toNumber() * item.discountRate.toNumber()) / 100 : 0));
                const taxRate = item.taxRate.toNumber();
                const taxAmount = (itemSubtotal * taxRate) / 100;
                const taxCode = item.product.tax.code;
                if (!taxBreakdown[taxCode]) {
                    taxBreakdown[taxCode] = { name: item.product.tax.name, rate: taxRate, base: 0, amount: 0 };
                }
                taxBreakdown[taxCode].base += itemSubtotal;
                taxBreakdown[taxCode].amount += taxAmount;
            }
            for (const item of order.items) {
                const categoryId = item.product.categoryId;
                const categoryName = item.product.category.name;
                const itemSubtotal = (item.unitPrice.toNumber() * item.quantity.toNumber()) - (item.discountAmount.toNumber() || (item.discountRate.toNumber() ? (item.unitPrice.toNumber() * item.discountRate.toNumber()) / 100 : 0));
                if (!categoryBreakdown[categoryId]) {
                    categoryBreakdown[categoryId] = { name: categoryName, grossSales: 0 };
                }
                categoryBreakdown[categoryId].grossSales += itemSubtotal;
            }
            const orderHour = order.orderedAt.getHours();
            const hourKey = `${String(orderHour).padStart(2, '0')}:00`;
            if (!hourlyBreakdown[hourKey]) {
                hourlyBreakdown[hourKey] = 0;
            }
            hourlyBreakdown[hourKey] += order.totalAmount.toNumber();
        }
        netSales = grossSales - totalDiscount + totalServiceCharge;
        let averageTicket = totalOrders > 0 ? totalSales / totalOrders : 0;
        totalCustomers = customerIds.size;
        let openingBalance = 0;
        let totalCashIn = 0;
        let totalCashOut = 0;
        for (const movement of cashMovements) {
            if (movement.type === client_1.CashMovementType.OPENING) {
                openingBalance = movement.amount.toNumber();
            }
            else if (movement.type === client_1.CashMovementType.SALE || movement.type === client_1.CashMovementType.INCOME || movement.type === client_1.CashMovementType.DEPOSIT || movement.type === client_1.CashMovementType.TRANSFER_IN || movement.type === client_1.CashMovementType.SURPLUS) {
                totalCashIn += movement.amount.toNumber();
            }
            else if (movement.type === client_1.CashMovementType.REFUND || movement.type === client_1.CashMovementType.EXPENSE || movement.type === client_1.CashMovementType.WITHDRAWAL || movement.type === client_1.CashMovementType.TRANSFER_OUT || movement.type === client_1.CashMovementType.SHORTAGE) {
                totalCashOut += Math.abs(movement.amount.toNumber());
            }
        }
        const expectedBalance = openingBalance + totalCashIn - totalCashOut;
        let actualBalance = null;
        let difference = null;
        return {
            reportDate: startOfDay,
            reportNo: await this.generateReportNumber(branchId, reportDate),
            totalOrders,
            totalItems,
            totalCustomers,
            averageTicket: parseFloat(averageTicket.toFixed(2)),
            grossSales: parseFloat(grossSales.toFixed(2)),
            totalDiscount: parseFloat(totalDiscount.toFixed(2)),
            totalServiceCharge: parseFloat(totalServiceCharge.toFixed(2)),
            netSales: parseFloat(netSales.toFixed(2)),
            totalTax: parseFloat(totalTax.toFixed(2)),
            totalSales: parseFloat(totalSales.toFixed(2)),
            cashSales: parseFloat(cashSales.toFixed(2)),
            creditCardSales: parseFloat(creditCardSales.toFixed(2)),
            debitCardSales: parseFloat(debitCardSales.toFixed(2)),
            mealCardSales: parseFloat(mealCardSales.toFixed(2)),
            otherSales: parseFloat(otherSales.toFixed(2)),
            totalReturns: parseFloat(totalReturns.toFixed(2)),
            totalCancellations: parseFloat(totalCancellations.toFixed(2)),
            openingBalance: parseFloat(openingBalance.toFixed(2)),
            totalCashIn: parseFloat(totalCashIn.toFixed(2)),
            totalCashOut: parseFloat(totalCashOut.toFixed(2)),
            expectedBalance: parseFloat(expectedBalance.toFixed(2)),
            actualBalance: actualBalance,
            difference: difference,
            taxBreakdown: taxBreakdown,
            categoryBreakdown: categoryBreakdown,
            hourlyBreakdown: hourlyBreakdown,
        };
    }
    async generateReportNumber(branchId, reportDate) {
        const currentYear = reportDate.getFullYear();
        const latestReport = await this.prisma.dailyReport.findFirst({
            where: { branchId, reportDate: { gte: new Date(`${currentYear}-01-01T00:00:00Z`) } },
            orderBy: { createdAt: 'desc' }
        });
        let sequence = 1;
        if (latestReport && latestReport.reportNo) {
            const lastSeq = parseInt(latestReport.reportNo.match(/\d+$/)?.[0] || '0');
            sequence = lastSeq + 1;
        }
        return `Z${String(sequence).padStart(6, '0')}`;
    }
    async createDailyReport(data) {
        const existingReport = await this.prisma.dailyReport.findUnique({
            where: {
                branchId_reportDate: {
                    branchId: data.branchId,
                    reportDate: new Date(data.reportDate).toISOString().split('T')[0] + 'T00:00:00.000Z',
                },
            },
        });
        if (existingReport) {
            throw new common_1.ConflictException(`Daily report for branch "${data.branchId}" on ${data.reportDate.toISOString().split('T')[0]} already exists.`);
        }
        const calculatedData = await this.generateReportData(data.branchId, new Date(data.reportDate));
        const createData = {
            branchId: data.branchId,
            createdBy: data.createdBy,
            reportNo: calculatedData.reportNo,
            reportDate: calculatedData.reportDate,
            totalOrders: calculatedData.totalOrders,
            totalItems: calculatedData.totalItems,
            totalCustomers: calculatedData.totalCustomers,
            averageTicket: calculatedData.averageTicket,
            grossSales: calculatedData.grossSales,
            totalDiscount: calculatedData.totalDiscount,
            totalServiceCharge: calculatedData.totalServiceCharge,
            netSales: calculatedData.netSales,
            totalTax: calculatedData.totalTax,
            totalSales: calculatedData.totalSales,
            cashSales: calculatedData.cashSales,
            creditCardSales: calculatedData.creditCardSales,
            debitCardSales: calculatedData.debitCardSales,
            mealCardSales: calculatedData.mealCardSales,
            otherSales: calculatedData.otherSales,
            totalReturns: calculatedData.totalReturns,
            totalCancellations: calculatedData.totalCancellations,
            openingBalance: calculatedData.openingBalance,
            totalCashIn: calculatedData.totalCashIn,
            totalCashOut: calculatedData.totalCashOut,
            expectedBalance: calculatedData.expectedBalance,
            actualBalance: calculatedData.expectedBalance,
            difference: 0,
            taxBreakdown: calculatedData.taxBreakdown,
            categoryBreakdown: calculatedData.categoryBreakdown,
            hourlyBreakdown: calculatedData.hourlyBreakdown,
        };
        if (data.actualBalance !== undefined) {
            createData.actualBalance = data.actualBalance;
        }
        if (data.difference !== undefined) {
            createData.difference = data.difference;
        }
        if (data.zReportNo) {
            createData.zReportNo = data.zReportNo;
        }
        if (data.fiscalId) {
            createData.fiscalId = data.fiscalId;
        }
        if (data.approvedBy) {
            createData.approvedBy = data.approvedBy;
        }
        return this.prisma.dailyReport.create({
            data: createData,
        });
    }
    async findAllDailyReports(branchId, startDate, endDate) {
        return this.prisma.dailyReport.findMany({
            where: {
                branchId: branchId || undefined,
                reportDate: {
                    gte: startDate ? new Date(startDate.toISOString().split('T')[0]) : undefined,
                    lte: endDate ? new Date(endDate.toISOString().split('T')[0]) : undefined,
                },
            },
            include: {
                branch: { select: { id: true, name: true } },
            },
            orderBy: { reportDate: 'desc' },
        });
    }
    async findOneDailyReport(id) {
        const report = await this.prisma.dailyReport.findUnique({
            where: { id },
            include: { branch: { select: { id: true, name: true } } },
        });
        if (!report) {
            throw new common_1.NotFoundException(`Daily report with ID "${id}" not found.`);
        }
        return report;
    }
    async updateDailyReport(id, data) {
        const existingReport = await this.findOneDailyReport(id);
        const allowedUpdates = {
            approvedBy: data.approvedBy,
            approvedAt: data.approvedAt,
            zReportNo: data.zReportNo,
            fiscalId: data.fiscalId,
            actualBalance: data.actualBalance,
            difference: data.difference,
            printedAt: data.printedAt,
            emailedAt: data.emailedAt,
        };
        try {
            return await this.prisma.dailyReport.update({
                where: { id },
                data: allowedUpdates,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Daily report with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeDailyReport(id) {
        try {
            return await this.prisma.dailyReport.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Daily report with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.DailyReportService = DailyReportService;
exports.DailyReportService = DailyReportService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DailyReportService);
//# sourceMappingURL=daily-report.service.js.map