"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
var __param = (this && this.__param) || function (paramIndex, decorator) {
    return function (target, key) { decorator(target, key, paramIndex); }
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashMovementController = void 0;
const common_1 = require("@nestjs/common");
const cash_movement_service_1 = require("./cash-movement.service");
const create_cash_movement_dto_1 = require("./dto/create-cash-movement.dto");
const update_cash_movement_dto_1 = require("./dto/update-cash-movement.dto");
const client_1 = require("@prisma/client");
let CashMovementController = class CashMovementController {
    cashMovementService;
    constructor(cashMovementService) {
        this.cashMovementService = cashMovementService;
    }
    create(createCashMovementDto) {
        return this.cashMovementService.createCashMovement(createCashMovementDto);
    }
    findAll(branchId, userId, type, startDate, endDate) {
        const parsedStartDate = startDate ? new Date(startDate) : undefined;
        const parsedEndDate = endDate ? new Date(endDate + 'T23:59:59Z') : undefined;
        return this.cashMovementService.findAllCashMovements(branchId, userId, type, parsedStartDate, parsedEndDate);
    }
    findOne(id) {
        return this.cashMovementService.findOneCashMovement(id);
    }
    update(id, updateCashMovementDto) {
        return this.cashMovementService.updateCashMovement(id, updateCashMovementDto);
    }
    remove(id) {
        return this.cashMovementService.removeCashMovement(id);
    }
};
exports.CashMovementController = CashMovementController;
__decorate([
    (0, common_1.Post)(),
    (0, common_1.HttpCode)(common_1.HttpStatus.CREATED),
    __param(0, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [create_cash_movement_dto_1.CreateCashMovementDto]),
    __metadata("design:returntype", void 0)
], CashMovementController.prototype, "create", null);
__decorate([
    (0, common_1.Get)(),
    __param(0, (0, common_1.Query)('branchId')),
    __param(1, (0, common_1.Query)('userId')),
    __param(2, (0, common_1.Query)('type')),
    __param(3, (0, common_1.Query)('startDate')),
    __param(4, (0, common_1.Query)('endDate')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, String, String, String, String]),
    __metadata("design:returntype", void 0)
], CashMovementController.prototype, "findAll", null);
__decorate([
    (0, common_1.Get)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CashMovementController.prototype, "findOne", null);
__decorate([
    (0, common_1.Patch)(':id'),
    __param(0, (0, common_1.Param)('id')),
    __param(1, (0, common_1.Body)()),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String, update_cash_movement_dto_1.UpdateCashMovementDto]),
    __metadata("design:returntype", void 0)
], CashMovementController.prototype, "update", null);
__decorate([
    (0, common_1.Delete)(':id'),
    (0, common_1.HttpCode)(common_1.HttpStatus.NO_CONTENT),
    __param(0, (0, common_1.Param)('id')),
    __metadata("design:type", Function),
    __metadata("design:paramtypes", [String]),
    __metadata("design:returntype", void 0)
], CashMovementController.prototype, "remove", null);
exports.CashMovementController = CashMovementController = __decorate([
    (0, common_1.Controller)('cash-movement'),
    __metadata("design:paramtypes", [cash_movement_service_1.CashMovementService])
], CashMovementController);
//# sourceMappingURL=cash-movement.controller.js.map