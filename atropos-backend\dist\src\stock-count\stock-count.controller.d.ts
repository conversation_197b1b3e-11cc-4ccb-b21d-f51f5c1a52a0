import { StockCountService } from './stock-count.service';
import { CreateStockCountDto } from './dto/create-stock-count.dto';
import { UpdateStockCountDto } from './dto/update-stock-count.dto';
import { StockCountType, StockCountStatus } from '@prisma/client';
export declare class StockCountController {
    private readonly stockCountService;
    constructor(stockCountService: StockCountService);
    create(createStockCountDto: CreateStockCountDto): Promise<{
        items: {
            id: string;
            note: string | null;
            difference: import("@prisma/client/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("@prisma/client/runtime/library").Decimal | null;
            countedQuantity: import("@prisma/client/runtime/library").Decimal;
            systemQuantity: import("@prisma/client/runtime/library").Decimal;
            totalDifference: import("@prisma/client/runtime/library").Decimal | null;
            stockCountId: string;
        }[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import(".prisma/client").$Enums.StockCountType;
        countedBy: string[];
    }>;
    findAll(branchId?: string, countType?: StockCountType, status?: StockCountStatus, startDate?: string, endDate?: string): Promise<({} & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import(".prisma/client").$Enums.StockCountType;
        countedBy: string[];
    })[]>;
    findOne(id: string): Promise<{
        items: ({
            inventoryItem: {
                id: string;
                name: string;
                unit: import(".prisma/client").$Enums.ProductUnit;
                currentStock: import("@prisma/client/runtime/library").Decimal;
                averageCost: import("@prisma/client/runtime/library").Decimal | null;
            };
        } & {
            id: string;
            note: string | null;
            difference: import("@prisma/client/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("@prisma/client/runtime/library").Decimal | null;
            countedQuantity: import("@prisma/client/runtime/library").Decimal;
            systemQuantity: import("@prisma/client/runtime/library").Decimal;
            totalDifference: import("@prisma/client/runtime/library").Decimal | null;
            stockCountId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import(".prisma/client").$Enums.StockCountType;
        countedBy: string[];
    }>;
    update(id: string, updateStockCountDto: UpdateStockCountDto): Promise<{
        items: ({
            inventoryItem: {
                id: string;
                name: string;
                unit: import(".prisma/client").$Enums.ProductUnit;
                currentStock: import("@prisma/client/runtime/library").Decimal;
                averageCost: import("@prisma/client/runtime/library").Decimal | null;
            };
        } & {
            id: string;
            note: string | null;
            difference: import("@prisma/client/runtime/library").Decimal;
            inventoryItemId: string;
            unitCost: import("@prisma/client/runtime/library").Decimal | null;
            countedQuantity: import("@prisma/client/runtime/library").Decimal;
            systemQuantity: import("@prisma/client/runtime/library").Decimal;
            totalDifference: import("@prisma/client/runtime/library").Decimal | null;
            stockCountId: string;
        })[];
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import(".prisma/client").$Enums.StockCountType;
        countedBy: string[];
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.StockCountStatus;
        completedAt: Date | null;
        note: string | null;
        startedAt: Date | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        countDate: Date;
        countType: import(".prisma/client").$Enums.StockCountType;
        countedBy: string[];
    }>;
}
