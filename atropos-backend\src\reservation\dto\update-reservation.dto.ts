// src/reservation/dto/update-reservation.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateReservationDto } from './create-reservation.dto';
import {
  IsString,
  IsOptional,
  IsDateString,
  IsEnum,
  IsBoolean,
  IsArray,
} from 'class-validator';
import { ReservationStatus } from '@prisma/client';

export class UpdateReservationDto extends PartialType(CreateReservationDto) {
  @IsOptional()
  @IsDateString()
  confirmedAt?: Date;

  @IsOptional()
  @IsDateString()
  cancelledAt?: Date;

  @IsOptional()
  @IsDateString()
  seatedAt?: Date;

  @IsOptional()
  @IsDateString()
  completedAt?: Date;

  @IsOptional()
  @IsDateString()
  reminderSentAt?: Date;

  @IsOptional()
  @IsBoolean()
  reminderSent?: boolean;

  @IsEnum(ReservationStatus)
  @IsOptional()
  status?: ReservationStatus;
}
