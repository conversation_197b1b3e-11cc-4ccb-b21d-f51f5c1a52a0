// src/loyalty-card/dto/create-loyalty-card.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  IsBoolean,
  IsEnum,
  IsDateString,
  IsNumber,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { LoyaltyCardType } from '@prisma/client'; // Enum'ı import et

export class CreateLoyaltyCardDto {
  @IsString()
  @IsNotEmpty()
  customerId: string; // Hangi müşteriye ait olduğu (benzersiz olmalı)

  @IsString()
  @IsNotEmpty()
  cardNumber: string; // Kart numarası (benzersiz olmalı)

  @IsEnum(LoyaltyCardType)
  @IsOptional()
  cardType?: LoyaltyCardType; // STANDARD, SILVER, GOLD vb.

  @IsInt()
  @Min(0)
  @IsOptional()
  points?: number; // Mevcut puan

  @IsInt()
  @Min(0)
  @IsOptional()
  totalEarnedPoints?: number; // Toplam kazanılan puan

  @IsInt()
  @Min(0)
  @IsOptional()
  totalSpentPoints?: number; // Toplam harcanan puan

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  balance?: number; // Para yüklemeli kartlar için bakiye

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  totalLoaded?: number; // Toplam yüklenen para

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsOptional()
  discountRate?: number; // Kart sahibine özel indirim oranı

  @IsString()
  @IsOptional()
  pin?: string; // encrypted

  @IsOptional()
  @IsDateString()
  issuedAt?: Date;

  @IsOptional()
  @IsDateString()
  activatedAt?: Date;

  @IsOptional()
  @IsDateString()
  expiresAt?: Date;

  @IsBoolean()
  @IsOptional()
  blocked?: boolean;

  @IsString()
  @IsOptional()
  blockReason?: string;

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
