// src/stock-count/dto/update-stock-count.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateStockCountDto, CreateStockCountItemDto } from './create-stock-count.dto';
import {
  IsString,
  IsOptional,
  IsDateString,
  IsEnum,
  IsArray,
  ValidateNested,
  IsNumber,
  Min,
  IsNotEmpty,
} from 'class-validator';
import { Type } from 'class-transformer';
import { StockCountStatus } from '@prisma/client';

// Güncelleme sırasında stok sayım kalemi için DTO
export class UpdateStockCountItemDto {
  @IsString()
  @IsOptional() // Yeni kalem ekleme durumunda ID olmayabilir
  id?: string;

  @IsString()
  @IsNotEmpty() // inventoryItemId her durumda gerekli
  inventoryItemId: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0)
  @IsNotEmpty()
  countedQuantity: number;

  @IsString()
  @IsOptional()
  note?: string;
}

export class UpdateStockCountDto {
  @IsString()
  @IsOptional()
  branchId?: string;

  @IsDateString()
  @IsOptional()
  countDate?: Date;

  @IsOptional()
  countType?: any;

  @IsEnum(StockCountStatus)
  @IsOptional()
  status?: StockCountStatus;

  @IsString()
  @IsOptional()
  note?: string;

  @IsOptional()
  @IsDateString()
  startedAt?: Date;

  @IsOptional()
  @IsDateString()
  completedAt?: Date;

  @IsOptional()
  @IsDateString()
  approvedAt?: Date;

  @IsString()
  @IsOptional()
  createdBy?: string;

  @IsOptional()
  @IsArray()
  @IsString({ each: true })
  countedBy?: string[];

  @IsString()
  @IsOptional()
  approvedBy?: string;

  @IsOptional()
  @IsArray()
  @ValidateNested({ each: true })
  @Type(() => UpdateStockCountItemDto)
  items?: UpdateStockCountItemDto[]; // Stok sayım kalemlerini güncellemek için
}
