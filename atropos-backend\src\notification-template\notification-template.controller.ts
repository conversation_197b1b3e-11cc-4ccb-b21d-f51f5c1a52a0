// src/notification-template/notification-template.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
  ParseBoolPipe,
} from '@nestjs/common';
import { NotificationTemplateService } from './notification-template.service';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationChannel } from '@prisma/client';
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe';

@Controller('notification-template')
export class NotificationTemplateController {
  constructor(private readonly notificationTemplateService: NotificationTemplateService) {}

  @Post() // POST /notification-template
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createNotificationTemplateDto: CreateNotificationTemplateDto) {
    return this.notificationTemplateService.createNotificationTemplate(createNotificationTemplateDto);
  }

  @Get() // GET /notification-template?companyId=...&channel=...&active=...
  findAll(
    @Query('companyId') companyId?: string,
    @Query('channel', new ParseOptionalEnumPipe(NotificationChannel)) channel?: NotificationChannel,
    @Query('active', new ParseBoolPipe({ optional: true })) active?: boolean,
  ) {
    return this.notificationTemplateService.findAllNotificationTemplates(companyId, channel, active);
  }

  @Get(':id') // GET /notification-template/:id
  findOne(@Param('id') id: string) {
    return this.notificationTemplateService.findOneNotificationTemplate(id);
  }

  @Patch(':id') // PATCH /notification-template/:id
  update(@Param('id') id: string, @Body() updateNotificationTemplateDto: UpdateNotificationTemplateDto) {
    return this.notificationTemplateService.updateNotificationTemplate(id, updateNotificationTemplateDto);
  }

  @Delete(':id') // DELETE /notification-template/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.notificationTemplateService.removeNotificationTemplate(id);
  }
}
