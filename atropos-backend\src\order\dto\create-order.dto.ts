// src/order/dto/create-order.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsInt,
  Min,
  IsEnum,
  IsNumber,
  ValidateNested,
  IsArray,
  ArrayMinSize,
  Max,
} from 'class-validator';
import { Type } from 'class-transformer';
import { OrderType, OrderStatus, PaymentStatus } from '@prisma/client';

// Sipariş Kalemi için ayrı bir DTO
export class CreateOrderItemDto {
  @IsString()
  @IsNotEmpty()
  productId: string;

  @IsString()
  @IsOptional()
  variantId?: string;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 3 })
  @Min(0.001)
  @IsNotEmpty()
  quantity: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsNotEmpty()
  unitPrice: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  costPrice?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  discountAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsOptional()
  discountRate?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsNotEmpty()
  taxRate: number;

  @IsString()
  @IsOptional()
  note?: string;

  @IsString()
  @IsOptional()
  guestName?: string; // "Masa 5 - Ali"

  @IsInt()
  @IsOptional()
  @Min(1)
  courseNumber?: number; // 1=Başlangıç, 2=Ana yemek, 3=Tatlı
}

export class CreateOrderDto {
  @IsString()
  @IsNotEmpty()
  branchId: string;

  @IsEnum(OrderType)
  @IsNotEmpty()
  orderType: OrderType;

  @IsString()
  @IsOptional()
  tableId?: string; // DINE_IN için gerekli olabilir

  @IsInt()
  @IsOptional()
  @Min(1)
  customerCount?: number;

  @IsString()
  @IsOptional()
  customerId?: string;

  @IsString()
  @IsOptional()
  customerName?: string;

  @IsString()
  @IsOptional()
  customerPhone?: string;

  @IsString()
  @IsOptional()
  deliveryAddress?: string; // DELIVERY için gerekli

  @IsString()
  @IsOptional()
  deliveryNote?: string;

  @IsString()
  @IsOptional()
  waiterId?: string;

  @IsString()
  @IsOptional()
  orderNote?: string;

  @IsString()
  @IsOptional()
  kitchenNote?: string;

  @IsString()
  @IsOptional()
  internalNote?: string;

  @IsInt()
  @IsOptional()
  @Min(0)
  estimatedTime?: number; // Tahmini hazırlık süresi (dakika)

  @IsString()
  @IsOptional()
  onlinePlatformId?: string;

  @IsString()
  @IsOptional()
  platformOrderId?: string;

  @IsString()
  @IsOptional()
  platformOrderNo?: string;

  // Sipariş kalemleri
  @IsArray()
  @ArrayMinSize(1)
  @ValidateNested({ each: true })
  @Type(() => CreateOrderItemDto)
  items: CreateOrderItemDto[];
}
