"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.NotificationTemplateService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let NotificationTemplateService = class NotificationTemplateService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createNotificationTemplate(data) {
        const companyExists = await this.prisma.company.findUnique({
            where: { id: data.companyId, deletedAt: null },
        });
        if (!companyExists) {
            throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
        }
        const existingTemplate = await this.prisma.notificationTemplate.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId,
                    code: data.code,
                },
            },
        });
        if (existingTemplate) {
            throw new common_1.ConflictException(`Notification template with code "${data.code}" already exists for this company.`);
        }
        if (data.channel === client_1.NotificationChannel.SMS && data.subject !== undefined) {
            throw new common_1.BadRequestException('Subject field should not be provided for SMS channel.');
        }
        if (data.channel === client_1.NotificationChannel.EMAIL && !data.subject) {
            throw new common_1.BadRequestException('Subject field is required for EMAIL channel.');
        }
        return this.prisma.notificationTemplate.create({ data });
    }
    async findAllNotificationTemplates(companyId, channel, active) {
        return this.prisma.notificationTemplate.findMany({
            where: {
                companyId: companyId || undefined,
                channel: channel || undefined,
                active: active !== undefined ? active : undefined,
            },
            include: { company: { select: { id: true, name: true } } },
            orderBy: { name: 'asc' },
        });
    }
    async findOneNotificationTemplate(id) {
        const template = await this.prisma.notificationTemplate.findUnique({
            where: { id },
            include: { company: { select: { id: true, name: true } } },
        });
        if (!template) {
            throw new common_1.NotFoundException(`Notification template with ID "${id}" not found.`);
        }
        return template;
    }
    async updateNotificationTemplate(id, data) {
        const existingTemplate = await this.findOneNotificationTemplate(id);
        if (data.companyId && data.companyId !== existingTemplate.companyId) {
            const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
            if (!companyExists) {
                throw new common_1.NotFoundException(`Company with ID "${data.companyId}" not found.`);
            }
        }
        if (data.code && data.code !== existingTemplate.code) {
            const existingTemplateByCode = await this.prisma.notificationTemplate.findUnique({
                where: {
                    companyId_code: {
                        companyId: data.companyId || existingTemplate.companyId,
                        code: data.code,
                    },
                },
            });
            if (existingTemplateByCode && existingTemplateByCode.id !== id) {
                throw new common_1.ConflictException(`Notification template with code "${data.code}" already exists for this company.`);
            }
        }
        const targetChannel = data.channel || existingTemplate.channel;
        if (targetChannel === client_1.NotificationChannel.SMS && data.subject !== undefined) {
            throw new common_1.BadRequestException('Subject field should not be provided for SMS channel.');
        }
        if (targetChannel === client_1.NotificationChannel.EMAIL && !data.subject && !existingTemplate.subject) {
            throw new common_1.BadRequestException('Subject field is required for EMAIL channel.');
        }
        try {
            return await this.prisma.notificationTemplate.update({
                where: { id },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Notification template with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeNotificationTemplate(id) {
        const logsCount = await this.prisma.notificationLog.count({
            where: { templateId: id }
        });
        if (logsCount > 0) {
            throw new common_1.ConflictException(`Notification template with ID "${id}" cannot be deleted because it has ${logsCount} associated logs.`);
        }
        try {
            return await this.prisma.notificationTemplate.delete({
                where: { id },
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Notification template with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.NotificationTemplateService = NotificationTemplateService;
exports.NotificationTemplateService = NotificationTemplateService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], NotificationTemplateService);
//# sourceMappingURL=notification-template.service.js.map