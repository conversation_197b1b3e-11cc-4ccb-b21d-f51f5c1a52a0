import { ReservationService } from './reservation.service';
import { CreateReservationDto } from './dto/create-reservation.dto';
import { UpdateReservationDto } from './dto/update-reservation.dto';
import { ReservationStatus } from '@prisma/client';
export declare class ReservationController {
    private readonly reservationService;
    constructor(reservationService: ReservationService);
    create(createReservationDto: CreateReservationDto): Promise<{
        branch: {
            id: string;
            name: string;
            address: string;
            phone: string;
            email: string | null;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            companyId: string;
            code: string;
            latitude: number | null;
            longitude: number | null;
            serverIp: string | null;
            serverPort: number | null;
            isMainBranch: boolean;
            openingTime: string | null;
            closingTime: string | null;
            workingDays: number[];
            cashRegisterId: string | null;
            posTerminalId: string | null;
            active: boolean;
        };
        customer: {
            id: string;
            taxNumber: string | null;
            taxOffice: string | null;
            address: string | null;
            phone: string;
            email: string | null;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            firstName: string | null;
            lastName: string | null;
            birthDate: Date | null;
            version: number;
            companyName: string | null;
            title: string | null;
            phone2: string | null;
            district: string | null;
            city: string | null;
            country: string | null;
            postalCode: string | null;
            gender: string | null;
            marketingConsent: boolean;
            smsConsent: boolean;
            emailConsent: boolean;
            loyaltyPoints: number;
            totalSpent: import("@prisma/client/runtime/library").Decimal;
            orderCount: number;
            lastOrderDate: Date | null;
            currentDebt: import("@prisma/client/runtime/library").Decimal;
            creditLimit: import("@prisma/client/runtime/library").Decimal;
            paymentTerm: number | null;
            segment: string | null;
            tags: string[];
            notes: string | null;
            source: string | null;
            referredBy: string | null;
            blacklisted: boolean;
            blacklistReason: string | null;
            customFields: import("@prisma/client/runtime/library").JsonValue | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import(".prisma/client").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("@prisma/client/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("@prisma/client/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
    findAll(branchId?: string, customerId?: string, status?: ReservationStatus, startDate?: Date, endDate?: Date): Promise<({
        branch: {
            id: string;
            name: string;
        };
        customer: {
            id: string;
            phone: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import(".prisma/client").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("@prisma/client/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("@prisma/client/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            id: string;
            name: string;
        };
        customer: {
            id: string;
            phone: string;
            firstName: string | null;
            lastName: string | null;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import(".prisma/client").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("@prisma/client/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("@prisma/client/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
    update(id: string, updateReservationDto: UpdateReservationDto): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import(".prisma/client").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("@prisma/client/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("@prisma/client/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        updatedAt: Date;
        branchId: string;
        status: import(".prisma/client").$Enums.ReservationStatus;
        completedAt: Date | null;
        customerId: string | null;
        customerName: string;
        customerPhone: string;
        confirmedAt: Date | null;
        cancelledAt: Date | null;
        source: import(".prisma/client").$Enums.ReservationSource;
        createdBy: string | null;
        cancelReason: string | null;
        customerEmail: string | null;
        reservationDate: Date;
        reservationTime: string;
        duration: number;
        guestCount: number;
        childCount: number;
        tableIds: string[];
        tablePreference: string | null;
        specialRequests: string | null;
        allergyInfo: string | null;
        occasionType: string | null;
        internalNotes: string | null;
        confirmationCode: string | null;
        confirmedBy: string | null;
        depositRequired: boolean;
        depositAmount: import("@prisma/client/runtime/library").Decimal | null;
        depositPaid: boolean;
        noShowFee: import("@prisma/client/runtime/library").Decimal | null;
        seatedAt: Date | null;
        reminderSentAt: Date | null;
        reminderSent: boolean;
        reservationStartDateTime: Date;
        reservationEndDateTime: Date;
    }>;
}
