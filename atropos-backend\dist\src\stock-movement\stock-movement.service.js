"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.StockMovementService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let StockMovementService = class StockMovementService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createStockMovement(data) {
        const branchExists = await this.prisma.branch.findUnique({ where: { id: data.branchId, deletedAt: null } });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        const createdByExists = await this.prisma.user.findUnique({ where: { id: data.createdBy, deletedAt: null } });
        if (!createdByExists) {
            throw new common_1.NotFoundException(`User (createdBy) with ID "${data.createdBy}" not found.`);
        }
        if (data.approvedBy) {
            const approvedByExists = await this.prisma.user.findUnique({ where: { id: data.approvedBy, deletedAt: null } });
            if (!approvedByExists) {
                throw new common_1.NotFoundException(`User (approvedBy) with ID "${data.approvedBy}" not found.`);
            }
        }
        if ((data.productId && data.inventoryItemId) || (!data.productId && !data.inventoryItemId)) {
            throw new common_1.BadRequestException('Either productId or inventoryItemId must be provided, but not both.');
        }
        let targetInventoryItem;
        let stockUpdateTarget;
        if (data.productId) {
            const product = await this.prisma.product.findUnique({ where: { id: data.productId, deletedAt: null } });
            if (!product) {
                throw new common_1.NotFoundException(`Product with ID "${data.productId}" not found.`);
            }
            targetInventoryItem = await this.prisma.inventoryItem.findFirst({ where: { productId: data.productId, deletedAt: null } });
            if (!targetInventoryItem) {
                targetInventoryItem = product;
                stockUpdateTarget = 'product';
            }
            else {
                stockUpdateTarget = 'inventoryItem';
            }
        }
        else {
            const inventoryItem = await this.prisma.inventoryItem.findUnique({ where: { id: data.inventoryItemId, deletedAt: null } });
            if (!inventoryItem) {
                throw new common_1.NotFoundException(`Inventory item with ID "${data.inventoryItemId}" not found.`);
            }
            targetInventoryItem = inventoryItem;
            stockUpdateTarget = 'inventoryItem';
        }
        if (targetInventoryItem.unit !== data.unit) {
            throw new common_1.BadRequestException(`Unit mismatch: Target item unit is ${targetInventoryItem.unit}, but movement unit is ${data.unit}.`);
        }
        let adjustedQuantity = parseFloat(data.quantity.toFixed(3));
        const negativeTypes = [
            client_1.StockMovementType.SALE, client_1.StockMovementType.RETURN_OUT, client_1.StockMovementType.WASTE,
            client_1.StockMovementType.DAMAGE, client_1.StockMovementType.THEFT, client_1.StockMovementType.TRANSFER_OUT,
            client_1.StockMovementType.CONSUMPTION, client_1.StockMovementType.SAMPLE, client_1.StockMovementType.GIFT,
            client_1.StockMovementType.MODIFIER_CONSUMPTION
        ];
        if (negativeTypes.includes(data.type)) {
            adjustedQuantity = -Math.abs(adjustedQuantity);
        }
        const previousStock = stockUpdateTarget === 'product' ? targetInventoryItem.currentStock.toNumber() : targetInventoryItem.currentStock.toNumber();
        const newCurrentStock = previousStock + adjustedQuantity;
        if (newCurrentStock < 0 && !['ADJUSTMENT'].includes(data.type)) {
            throw new common_1.BadRequestException(`Insufficient stock for this movement. Current stock: ${previousStock.toFixed(3)} ${data.unit}. Attempted movement: ${adjustedQuantity.toFixed(3)} ${data.unit}.`);
        }
        let previousCost = targetInventoryItem.averageCost?.toNumber() || 0;
        let newAverageCost = previousCost;
        let totalCost = 0;
        if (data.type === client_1.StockMovementType.PURCHASE) {
            if (data.unitCost === undefined || data.unitCost < 0) {
                throw new common_1.BadRequestException('Unit cost must be provided and positive for PURCHASE movements.');
            }
            const incomingCost = data.unitCost * data.quantity;
            totalCost = parseFloat(incomingCost.toFixed(2));
            if (previousStock + data.quantity > 0) {
                newAverageCost = ((previousStock * previousCost) + incomingCost) / (previousStock + data.quantity);
            }
            else {
                newAverageCost = data.unitCost;
            }
        }
        else {
            totalCost = Math.abs(adjustedQuantity) * previousCost;
            newAverageCost = previousCost;
        }
        const createdMovement = await this.prisma.stockMovement.create({
            data: {
                ...data,
                quantity: adjustedQuantity,
                unitCost: data.unitCost !== undefined ? parseFloat(data.unitCost.toFixed(2)) : undefined,
                totalCost: parseFloat(totalCost.toFixed(2)),
                previousCost: parseFloat(previousCost.toFixed(2)),
                newAverageCost: parseFloat(newAverageCost.toFixed(2)),
                previousStock: parseFloat(previousStock.toFixed(3)),
                currentStock: parseFloat(newCurrentStock.toFixed(3)),
                productId: data.productId,
                inventoryItemId: data.inventoryItemId,
            },
        });
        const updateData = {
            currentStock: parseFloat(newCurrentStock.toFixed(3)),
            availableStock: parseFloat((newCurrentStock - (targetInventoryItem.reservedStock?.toNumber() || 0)).toFixed(3)),
            averageCost: parseFloat(newAverageCost.toFixed(2)),
        };
        if (stockUpdateTarget === 'product') {
        }
        else {
            await this.prisma.inventoryItem.update({
                where: { id: targetInventoryItem.id },
                data: updateData,
            });
        }
        return createdMovement;
    }
    async findAllStockMovements(branchId, productId, inventoryItemId, type, startDate, endDate) {
        return this.prisma.stockMovement.findMany({
            where: {
                branchId: branchId || undefined,
                productId: productId || undefined,
                inventoryItemId: inventoryItemId || undefined,
                type: type || undefined,
                createdAt: {
                    gte: startDate || undefined,
                    lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined,
                },
            },
            include: {
                branch: { select: { id: true, name: true } },
                product: { select: { id: true, name: true, code: true } },
                inventoryItem: { select: { id: true, name: true, code: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneStockMovement(id) {
        const movement = await this.prisma.stockMovement.findUnique({
            where: { id },
            include: {
                branch: { select: { id: true, name: true } },
                product: { select: { id: true, name: true, code: true } },
                inventoryItem: { select: { id: true, name: true, code: true } },
            },
        });
        if (!movement) {
            throw new common_1.NotFoundException(`Stock movement with ID "${id}" not found.`);
        }
        return movement;
    }
    async updateStockMovement(id, data) {
        const existingMovement = await this.findOneStockMovement(id);
        if (data.quantity !== undefined || data.type !== undefined || data.unit !== undefined || data.unitCost !== undefined ||
            data.productId !== undefined || data.inventoryItemId !== undefined || data.branchId !== undefined || data.createdBy !== undefined) {
            throw new common_1.ForbiddenException('Only "approvedBy", "approvedAt", "reason", "note", "referenceNo", "attachments" can be updated for stock movements. For other changes, consider voiding and re-creating the movement.');
        }
        try {
            return await this.prisma.stockMovement.update({
                where: { id },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Stock movement with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeStockMovement(id) {
        try {
            const movement = await this.prisma.stockMovement.delete({
                where: { id },
            });
            return movement;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Stock movement with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.StockMovementService = StockMovementService;
exports.StockMovementService = StockMovementService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], StockMovementService);
//# sourceMappingURL=stock-movement.service.js.map