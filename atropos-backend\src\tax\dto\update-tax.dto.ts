// src/tax/dto/update-tax.dto.ts
import { PartialType } from '@nestjs/mapped-types';
import { CreateTaxDto } from './create-tax.dto';
import { IsOptional, IsDecimal, Min, Max, IsEnum, IsBoolean } from 'class-validator';
import { Type } from 'class-transformer';
import { TaxType } from '@prisma/client';

export class UpdateTaxDto extends PartialType(CreateTaxDto) {
    @IsOptional()
    @Type(() => Number)
    @Min(0)
    @Max(100)
    rate?: number;

    @IsOptional()
    @IsEnum(TaxType)
    type?: TaxType;

    @IsOptional()
    @IsBoolean()
    isDefault?: boolean;

    @IsOptional()
    @IsBoolean()
    isIncluded?: boolean;

    @IsOptional()
    @IsBoolean()
    active?: boolean;
}
