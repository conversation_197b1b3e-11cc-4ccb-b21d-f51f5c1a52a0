import React from 'react';

const earningIcon = "http://localhost:3845/assets/ef326b5f282eca6a38f11a2de1c35e128d853d78.svg";
const progressIcon = "http://localhost:3845/assets/813c1bc1faf5223349261afa15debea831476d6e.svg";
const waitingIcon = "http://localhost:3845/assets/3fcc6bf3b59c9b4a9fc4b74cfd0ddf4c0a74a3d1.svg";

interface OverviewData {
  totalEarning: number;
  totalEarningChange: number;
  inProgress: number;
  inProgressChange: number;
  waitingList: number;
  waitingListChange: number;
}

interface OverviewCardsProps {
  data: OverviewData | null;
}

export default function OverviewCards({ data }: OverviewCardsProps) {
  const formatChange = (change: number) => {
    const sign = change >= 0 ? '+' : '';
    return `${sign}${change.toFixed(1)}%`;
  };

  const getChangeColor = (change: number) => {
    return change >= 0 ? 'text-success-500' : 'text-error-500';
  };

  return (
    <div className="grid grid-cols-1 md:grid-cols-3 gap-6">
      {/* Total Earning Card */}
      <div className="bg-neutral-black rounded-2xl p-6 border border-neutral-700">
        <div className="flex items-start justify-between mb-4">
          <div className="bg-primary-100 rounded-xl p-3">
            <img src={earningIcon} alt="Earning" className="w-6 h-6" />
          </div>
          <div className="text-right">
            <p className="text-body-4 text-neutral-400 font-inter mb-1">Total Earning</p>
            <p className="text-large-1 text-neutral-white font-inter">
              ${data?.totalEarning || 0}
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className="bg-success-100 rounded-lg px-2 py-1">
            <span className="text-title-5 text-success-700 font-inter">
              {formatChange(data?.totalEarningChange || 0)}
            </span>
          </div>
          <p className="text-body-5 text-neutral-400 font-inter">than yesterday</p>
        </div>
      </div>

      {/* In Progress Card */}
      <div className="bg-neutral-black rounded-2xl p-6 border border-neutral-700">
        <div className="flex items-start justify-between mb-4">
          <div className="bg-warning-100 rounded-xl p-3">
            <img src={progressIcon} alt="Progress" className="w-6 h-6" />
          </div>
          <div className="text-right">
            <p className="text-body-4 text-neutral-400 font-inter mb-1">In Progress</p>
            <p className="text-large-1 text-neutral-white font-inter">
              {data?.inProgress || 0}
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className={`rounded-lg px-2 py-1 ${
            (data?.inProgressChange || 0) >= 0 ? 'bg-success-100' : 'bg-error-500/10'
          }`}>
            <span className={`text-title-5 font-inter ${
              (data?.inProgressChange || 0) >= 0 ? 'text-success-700' : 'text-error-500'
            }`}>
              {formatChange(data?.inProgressChange || 0)}
            </span>
          </div>
          <p className="text-body-5 text-neutral-400 font-inter">than yesterday</p>
        </div>
      </div>

      {/* Waiting List Card */}
      <div className="bg-neutral-black rounded-2xl p-6 border border-neutral-700">
        <div className="flex items-start justify-between mb-4">
          <div className="bg-primary-100 rounded-xl p-3">
            <img src={waitingIcon} alt="Waiting" className="w-6 h-6" />
          </div>
          <div className="text-right">
            <p className="text-body-4 text-neutral-400 font-inter mb-1">Waiting List</p>
            <p className="text-large-1 text-neutral-white font-inter">
              {data?.waitingList || 0}
            </p>
          </div>
        </div>
        <div className="flex items-center justify-between">
          <div className={`rounded-lg px-2 py-1 ${
            (data?.waitingListChange || 0) >= 0 ? 'bg-success-100' : 'bg-error-500/10'
          }`}>
            <span className={`text-title-5 font-inter ${
              (data?.waitingListChange || 0) >= 0 ? 'text-success-700' : 'text-error-500'
            }`}>
              {formatChange(data?.waitingListChange || 0)}
            </span>
          </div>
          <p className="text-body-5 text-neutral-400 font-inter">than yesterday</p>
        </div>
      </div>
    </div>
  );
}
