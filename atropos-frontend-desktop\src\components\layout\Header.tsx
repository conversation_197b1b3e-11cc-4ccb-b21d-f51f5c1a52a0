import React from 'react';

const searchIcon = "http://localhost:3845/assets/17ecfe562400c8146f7d09317c3599c2dae738da.svg";
const notificationIcon = "http://localhost:3845/assets/1d8ff63be3727469ae754af486846354f5dab635.svg";
const userAvatar = "http://localhost:3845/assets/ac24ce3d9c958409a1f8e5eeca3b3a080e1ee11d.png";

interface HeaderProps {
  searchTerm: string;
  onSearchChange: (value: string) => void;
  onLogout: () => void;
}

export default function Header({ searchTerm, onSearchChange, onLogout }: HeaderProps) {
  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const getCurrentDate = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', { 
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  return (
    <div className="bg-neutral-black px-6 py-5">
      {/* Top Row */}
      <div className="flex items-center justify-between mb-6">
        {/* Left - Brand and Search */}
        <div className="flex items-center gap-8">
          {/* Brand */}
          <div>
            <h1 className="text-title-3 text-neutral-white font-inter">Aleo Resto</h1>
            <p className="text-body-5 text-neutral-400 font-inter">Merkez Şube</p>
          </div>
          
          {/* Search */}
          <div className="relative">
            <div className="bg-neutral-800 rounded-xl px-4 py-3 flex items-center gap-3 w-80">
              <img src={searchIcon} alt="Search" className="w-6 h-6" />
              <input
                type="text"
                placeholder="Search a Order"
                value={searchTerm}
                onChange={(e) => onSearchChange(e.target.value)}
                className="bg-transparent text-body-4 text-neutral-white placeholder-neutral-500 outline-none flex-1 font-inter"
              />
            </div>
          </div>
        </div>

        {/* Right - Notifications and User */}
        <div className="flex items-center gap-6">
          {/* Notifications */}
          <div className="relative">
            <div className="bg-neutral-800 rounded-xl p-3">
              <img src={notificationIcon} alt="Notifications" className="w-6 h-6" />
            </div>
            <div className="absolute -top-1 -right-1 bg-error-500 text-neutral-white text-title-5 rounded-full w-5 h-5 flex items-center justify-center font-inter">
              3
            </div>
          </div>

          {/* User Profile */}
          <div className="flex items-center gap-3">
            <img 
              src={userAvatar} 
              alt="User Avatar" 
              className="w-10 h-10 rounded-xl"
            />
            <div className="text-right">
              <p className="text-title-4 text-neutral-white font-inter">Admin User</p>
              <p className="text-body-5 text-neutral-400 font-inter">Manager</p>
            </div>
          </div>

          {/* Logout Button */}
          <button
            onClick={onLogout}
            className="bg-error-500 hover:bg-red-600 text-neutral-white px-4 py-2 rounded-xl text-body-4 font-inter transition-colors"
          >
            Logout
          </button>
        </div>
      </div>

      {/* Bottom Row - Greeting */}
      <div>
        <h2 className="text-subtitle-1 text-neutral-white font-inter mb-1">
          Good Morning, Admin User
        </h2>
        <p className="text-body-4 text-neutral-400 font-inter">
          {getCurrentDate()} - {getCurrentTime()}
        </p>
      </div>
    </div>
  );
}
