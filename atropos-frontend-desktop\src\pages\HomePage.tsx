import { useState, useEffect } from 'react';

// Figma assets
const img5 = "http://localhost:3845/assets/ac24ce3d9c958409a1f8e5eeca3b3a080e1ee11d.png";
const imgEmoticon = "http://localhost:3845/assets/460adc1630bdb63212d3f5537838c8d598002f02.png";
const img4 = "http://localhost:3845/assets/17ecfe562400c8146f7d09317c3599c2dae738da.svg";
const img2 = "http://localhost:3845/assets/1d8ff63be3727469ae754af486846354f5dab635.svg";
const img1 = "http://localhost:3845/assets/ef326b5f282eca6a38f11a2de1c35e128d853d78.svg";
const img = "http://localhost:3845/assets/813c1bc1faf5223349261afa15debea831476d6e.svg";
const img3 = "http://localhost:3845/assets/3fcc6bf3b59c9b4a9fc4b74cfd0ddf4c0a74a3d1.svg";

interface OverviewData {
  totalEarning: number;
  totalEarningChange: number;
  inProgress: number;
  inProgressChange: number;
  waitingList: number;
  waitingListChange: number;
}

interface Order {
  id: string;
  tableId: string;
  customerName: string;
  itemCount: number;
  status: string;
  totalAmount?: number;
}

export default function HomePage() {
  const [overviewData, setOverviewData] = useState<OverviewData | null>(null);
  const [inProgressOrders, setInProgressOrders] = useState<Order[]>([]);
  const [waitingPaymentOrders, setWaitingPaymentOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'inProgress' | 'waitingPayment'>('inProgress');

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Dashboard overview verilerini çek
      const overviewResponse = await fetch(`${API_URL}/dashboard/overview`, { headers });
      if (!overviewResponse.ok) {
        if (overviewResponse.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
          return;
        }
        throw new Error(`Overview API error: ${overviewResponse.status}`);
      }
      const overviewData = await overviewResponse.json();
      setOverviewData(overviewData);

      // In Progress siparişlerini çek
      const inProgressResponse = await fetch(
        `${API_URL}/order?status=PREPARING,READY,SERVING`, 
        { headers }
      );
      if (inProgressResponse.ok) {
        const inProgressData = await inProgressResponse.json();
        const formattedInProgress = inProgressData.map((order: any) => ({
          id: order.id,
          tableId: order.table?.name || order.tableId || 'N/A',
          customerName: order.customerName || 'Guest',
          itemCount: order.items?.length || 0,
          status: getDisplayStatus(order.status),
        }));
        setInProgressOrders(formattedInProgress);
      }

      // Waiting for Payment siparişlerini çek
      const waitingPaymentResponse = await fetch(
        `${API_URL}/order?paymentStatus=UNPAID,PENDING`, 
        { headers }
      );
      if (waitingPaymentResponse.ok) {
        const waitingPaymentData = await waitingPaymentResponse.json();
        const formattedWaitingPayment = waitingPaymentData.map((order: any) => ({
          id: order.id,
          tableId: order.table?.name || order.tableId || 'N/A',
          customerName: order.customerName || 'Guest',
          itemCount: order.items?.length || 0,
          status: 'Waiting Payment',
          totalAmount: order.totalAmount || 0,
        }));
        setWaitingPaymentOrders(formattedWaitingPayment);
      }

    } catch (err: any) {
      if (err.message.includes('401')) {
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        return;
      }
      setError(err.message);
      console.error("Error fetching dashboard data:", err);
      
      // Fallback statik veri
      setOverviewData({
        totalEarning: 526,
        totalEarningChange: 3.2,
        inProgress: 12,
        inProgressChange: -1.5,
        waitingList: 8,
        waitingListChange: 2.1,
      });

      setInProgressOrders([
        { id: '1', tableId: 'A9', customerName: 'Adam Hamzah', itemCount: 8, status: 'Ready to serve' },
        { id: '2', tableId: 'A5', customerName: 'Nina Renard', itemCount: 4, status: 'Cooking Now' },
        { id: '3', tableId: 'A3', customerName: 'John Smith', itemCount: 6, status: 'In the Kitchen' },
      ]);

      setWaitingPaymentOrders([
        { id: '6', tableId: 'A13', customerName: 'David Owens', itemCount: 5, status: 'Waiting Payment', totalAmount: 125.50 },
        { id: '7', tableId: 'A15', customerName: 'Olivia Mason', itemCount: 4, status: 'Waiting Payment', totalAmount: 89.75 },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getDisplayStatus = (status: string) => {
    switch (status) {
      case 'PREPARING':
        return 'Cooking Now';
      case 'READY':
        return 'Ready to serve';
      case 'SERVING':
        return 'In the Kitchen';
      default:
        return status;
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    window.location.href = '/login';
  };

  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('en-US', { 
      hour: '2-digit', 
      minute: '2-digit',
      hour12: true 
    });
  };

  const getCurrentDate = () => {
    const now = new Date();
    return now.toLocaleDateString('en-US', { 
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const filteredInProgressOrders = inProgressOrders.filter(order =>
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredWaitingPaymentOrders = waitingPaymentOrders.filter(order =>
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ready to serve':
        return '#50D794';
      case 'Cooking Now':
        return '#E49527';
      case 'In the Kitchen':
        return '#357DD5';
      default:
        return '#797B7C';
    }
  };

  const getTableColor = (index: number) => {
    const colors = ['#357DD5', '#50D794', '#E49527', '#EE4E4F'];
    return colors[index % colors.length];
  };

  return (
    <div className="bg-[#292c2d] relative w-full h-screen font-['Inter',_sans-serif]">
      {error && (
        <div className="absolute top-4 left-1/2 transform -translate-x-1/2 bg-[#EE4E4F]/10 border border-[#EE4E4F] text-[#EE4E4F] px-4 py-3 rounded-xl z-50">
          Error: {error}
        </div>
      )}

      {loading ? (
        <div className="flex justify-center items-center h-full">
          <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-[#357DD5]"></div>
        </div>
      ) : (
        <>
          {/* Header Section */}
          <div className="absolute left-6 top-6 flex items-center gap-8">
            {/* Brand */}
            <div>
              <h1 className="text-[#FFFFFF] text-base font-semibold leading-6">Aleo Resto</h1>
              <p className="text-[#A2A4A4] text-xs font-normal leading-[18px]">Merkez Şube</p>
            </div>

            {/* Search */}
            <div className="bg-[#202325] rounded-xl px-4 py-3 flex items-center gap-3 w-80">
              <img src={img4} alt="Search" className="w-6 h-6" />
              <input
                type="text"
                placeholder="Search a Order"
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="bg-transparent text-[#FFFFFF] text-sm font-normal leading-[21px] placeholder-[#797B7C] outline-none flex-1"
              />
            </div>
          </div>

          {/* Top Right - User Info */}
          <div className="absolute right-6 top-6 flex items-center gap-6">
            {/* Notifications */}
            <div className="relative">
              <div className="bg-[#202325] rounded-xl p-3">
                <img src={img2} alt="Notifications" className="w-6 h-6" />
              </div>
              <div className="absolute -top-1 -right-1 bg-[#EE4E4F] text-[#FFFFFF] text-xs font-semibold rounded-full w-5 h-5 flex items-center justify-center">
                3
              </div>
            </div>

            {/* User Profile */}
            <div className="flex items-center gap-3">
              <img
                src={img5}
                alt="User Avatar"
                className="w-10 h-10 rounded-xl"
              />
              <div className="text-right">
                <p className="text-[#FFFFFF] text-sm font-semibold leading-[21px]">Admin User</p>
                <p className="text-[#A2A4A4] text-xs font-normal leading-[18px]">Manager</p>
              </div>
            </div>

            {/* Logout Button */}
            <button
              onClick={handleLogout}
              className="bg-[#EE4E4F] hover:bg-red-600 text-[#FFFFFF] px-4 py-2 rounded-xl text-sm font-normal transition-colors"
            >
              Logout
            </button>
          </div>

          {/* Greeting */}
          <div className="absolute left-6 top-24">
            <h2 className="text-[#FFFFFF] text-2xl font-medium leading-9 mb-1">
              Good Morning, Admin User
            </h2>
            <p className="text-[#A2A4A4] text-sm font-normal leading-[21px]">
              {getCurrentDate()} - {getCurrentTime()}
            </p>
          </div>

          {/* Overview Cards - Left Side */}
          <div className="absolute left-6 top-40 flex flex-col gap-6">
            {/* Total Earning Card */}
            <div className="bg-[#202325] rounded-2xl p-6 w-[400px]">
              <div className="flex items-start justify-between mb-4">
                <div className="bg-[#D9E7F7] rounded-xl p-3">
                  <img src={img1} alt="Earning" className="w-6 h-6" />
                </div>
                <div className="text-right">
                  <p className="text-[#A2A4A4] text-sm font-normal leading-[21px] mb-1">Total Earning</p>
                  <p className="text-[#FFFFFF] text-4xl font-semibold leading-[43.2px]">
                    ${overviewData?.totalEarning || 0}
                  </p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="bg-[#DCF7EA] rounded-lg px-2 py-1">
                  <span className="text-[#40AC76] text-xs font-semibold leading-[18px]">
                    +{Math.abs(overviewData?.totalEarningChange || 0)}%
                  </span>
                </div>
                <p className="text-[#A2A4A4] text-xs font-normal leading-[18px]">than yesterday</p>
              </div>
            </div>

            {/* In Progress Card */}
            <div className="bg-[#202325] rounded-2xl p-6 w-[400px]">
              <div className="flex items-start justify-between mb-4">
                <div className="bg-[#FFEDD5] rounded-xl p-3">
                  <img src={img} alt="Progress" className="w-6 h-6" />
                </div>
                <div className="text-right">
                  <p className="text-[#A2A4A4] text-sm font-normal leading-[21px] mb-1">In Progress</p>
                  <p className="text-[#FFFFFF] text-4xl font-semibold leading-[43.2px]">
                    {overviewData?.inProgress || 0}
                  </p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="bg-[#DCF7EA] rounded-lg px-2 py-1">
                  <span className="text-[#40AC76] text-xs font-semibold leading-[18px]">
                    +{Math.abs(overviewData?.inProgressChange || 0)}%
                  </span>
                </div>
                <p className="text-[#A2A4A4] text-xs font-normal leading-[18px]">than yesterday</p>
              </div>
            </div>

            {/* Waiting List Card */}
            <div className="bg-[#202325] rounded-2xl p-6 w-[400px]">
              <div className="flex items-start justify-between mb-4">
                <div className="bg-[#D9E7F7] rounded-xl p-3">
                  <img src={img3} alt="Waiting" className="w-6 h-6" />
                </div>
                <div className="text-right">
                  <p className="text-[#A2A4A4] text-sm font-normal leading-[21px] mb-1">Waiting List</p>
                  <p className="text-[#FFFFFF] text-4xl font-semibold leading-[43.2px]">
                    {overviewData?.waitingList || 0}
                  </p>
                </div>
              </div>
              <div className="flex items-center justify-between">
                <div className="bg-[#DCF7EA] rounded-lg px-2 py-1">
                  <span className="text-[#40AC76] text-xs font-semibold leading-[18px]">
                    +{Math.abs(overviewData?.waitingListChange || 0)}%
                  </span>
                </div>
                <p className="text-[#A2A4A4] text-xs font-normal leading-[18px]">than yesterday</p>
              </div>
            </div>
          </div>

          {/* Order List - Right Side */}
          <div className="absolute right-6 top-40 bg-[#202325] rounded-2xl p-5 w-[400px] h-[600px]">
            {/* Tabs */}
            <div className="bg-[#292C2D] rounded-xl p-0 mb-6">
              <div className="flex">
                <button
                  onClick={() => setActiveTab('inProgress')}
                  className={`flex-1 px-3 py-3 rounded-xl text-sm font-medium transition-colors ${
                    activeTab === 'inProgress'
                      ? 'bg-[#37383A] text-[#FFFFFF] shadow-sm'
                      : 'bg-[#292C2D] text-[#A2A4A4]'
                  }`}
                >
                  In Progress
                </button>
                <button
                  onClick={() => setActiveTab('waitingPayment')}
                  className={`flex-1 px-3 py-3 rounded-xl text-sm font-medium transition-colors ${
                    activeTab === 'waitingPayment'
                      ? 'bg-[#37383A] text-[#FFFFFF] shadow-sm'
                      : 'bg-[#292C2D] text-[#A2A4A4]'
                  }`}
                >
                  Waiting for Payment
                </button>
              </div>
            </div>

            {/* Search Input */}
            <div className="mb-6">
              <div className="bg-[#292C2D] rounded-xl px-4 py-3 flex items-center gap-3">
                <img src={img4} alt="Search" className="w-6 h-6" />
                <input
                  type="text"
                  placeholder="Search a Order"
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                  className="bg-transparent text-[#FFFFFF] text-sm font-normal placeholder-[#797B7C] outline-none flex-1"
                />
              </div>
            </div>

            {/* Order List Content */}
            <div className="space-y-6 overflow-y-auto h-[400px]">
              {activeTab === 'inProgress' && (
                <>
                  {filteredInProgressOrders.length === 0 ? (
                    <p className="text-[#A2A4A4] text-center py-8">No orders in progress</p>
                  ) : (
                    filteredInProgressOrders.map((order, index) => (
                      <div key={order.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div
                            className="rounded-xl w-[51px] h-[51px] flex items-center justify-center"
                            style={{ backgroundColor: getTableColor(index) }}
                          >
                            <span className="text-[#FFFFFF] text-sm font-semibold">
                              {order.tableId}
                            </span>
                          </div>
                          <div>
                            <p className="text-[#FFFFFF] text-sm font-semibold mb-1">
                              {order.customerName}
                            </p>
                            <p className="text-[#C6C7C8] text-xs font-normal">
                              {order.itemCount} Items
                            </p>
                          </div>
                        </div>
                        <div className="flex items-center gap-2">
                          <div
                            className="w-2 h-2 rounded-full"
                            style={{ backgroundColor: getStatusColor(order.status) }}
                          ></div>
                          <span className="text-[#C6C7C8] text-xs font-normal">
                            {order.status}
                          </span>
                        </div>
                      </div>
                    ))
                  )}
                </>
              )}

              {activeTab === 'waitingPayment' && (
                <>
                  {filteredWaitingPaymentOrders.length === 0 ? (
                    <p className="text-[#A2A4A4] text-center py-8">No orders waiting for payment</p>
                  ) : (
                    filteredWaitingPaymentOrders.map((order, index) => (
                      <div key={order.id} className="flex items-center justify-between">
                        <div className="flex items-center gap-4">
                          <div
                            className="rounded-xl w-[51px] h-[51px] flex items-center justify-center"
                            style={{ backgroundColor: getTableColor(index) }}
                          >
                            <span className="text-[#FFFFFF] text-sm font-semibold">
                              {order.tableId}
                            </span>
                          </div>
                          <div>
                            <p className="text-[#FFFFFF] text-sm font-semibold mb-1">
                              {order.customerName}
                            </p>
                            <p className="text-[#C6C7C8] text-xs font-normal">
                              {order.itemCount} Items • ${order.totalAmount?.toFixed(2)}
                            </p>
                          </div>
                        </div>
                        <button className="bg-[#357DD5] hover:bg-[#025CCA] text-[#FFFFFF] px-4 py-2 rounded-xl text-xs font-medium transition-colors">
                          Pay Now
                        </button>
                      </div>
                    ))
                  )}
                </>
              )}
            </div>
          </div>
        </>
      )}
    </div>
  );
}
