
import { useState, useEffect } from 'react';

interface OverviewData {
  totalEarning: number;
  totalEarningChange: number;
  inProgress: number;
  inProgressChange: number;
  waitingList: number;
  waitingListChange: number;
}

interface Order {
  id: string;
  tableId: string;
  customerName: string;
  itemCount: number;
  status: string;
  totalAmount?: number;
}

export default function HomePage() {
  const [overviewData, setOverviewData] = useState<OverviewData | null>(null);
  const [inProgressOrders, setInProgressOrders] = useState<Order[]>([]);
  const [waitingPaymentOrders, setWaitingPaymentOrders] = useState<Order[]>([]);
  const [loading, setLoading] = useState<boolean>(true);
  const [error, setError] = useState<string | null>(null);
  const [searchTerm, setSearchTerm] = useState<string>('');
  const [activeTab, setActiveTab] = useState<'inProgress' | 'waitingPayment'>('inProgress');

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  useEffect(() => {
    fetchDashboardData();
  }, []);

  const fetchDashboardData = async () => {
    setLoading(true);
    setError(null);
    try {
      const token = localStorage.getItem('access_token');
      const headers = {
        'Authorization': `Bearer ${token}`,
        'Content-Type': 'application/json',
      };

      // Dashboard overview verilerini çek
      const overviewResponse = await fetch(`${API_URL}/dashboard/overview`, { headers });
      if (!overviewResponse.ok) {
        if (overviewResponse.status === 401) {
          localStorage.removeItem('access_token');
          window.location.href = '/login';
          return;
        }
        throw new Error(`Overview API error: ${overviewResponse.status}`);
      }
      const overviewData = await overviewResponse.json();
      setOverviewData(overviewData);

      // In Progress siparişlerini çek (PREPARING, READY, SERVING durumları)
      const inProgressResponse = await fetch(
        `${API_URL}/order?status=PREPARING,READY,SERVING`,
        { headers }
      );
      if (inProgressResponse.ok) {
        const inProgressData = await inProgressResponse.json();
        const formattedInProgress = inProgressData.map((order: any) => ({
          id: order.id,
          tableId: order.table?.name || order.tableId || 'N/A',
          customerName: order.customerName || 'Guest',
          itemCount: order.items?.length || 0,
          status: getDisplayStatus(order.status),
        }));
        setInProgressOrders(formattedInProgress);
      }

      // Waiting for Payment siparişlerini çek (UNPAID, PENDING payment status)
      const waitingPaymentResponse = await fetch(
        `${API_URL}/order?paymentStatus=UNPAID,PENDING`,
        { headers }
      );
      if (waitingPaymentResponse.ok) {
        const waitingPaymentData = await waitingPaymentResponse.json();
        const formattedWaitingPayment = waitingPaymentData.map((order: any) => ({
          id: order.id,
          tableId: order.table?.name || order.tableId || 'N/A',
          customerName: order.customerName || 'Guest',
          itemCount: order.items?.length || 0,
          status: 'Waiting Payment',
          totalAmount: order.totalAmount || 0,
        }));
        setWaitingPaymentOrders(formattedWaitingPayment);
      }

    } catch (err: any) {
      if (err.message.includes('401')) {
        localStorage.removeItem('access_token');
        window.location.href = '/login';
        return;
      }
      setError(err.message);
      console.error("Error fetching dashboard data:", err);

      // Hata durumunda fallback statik veri
      setOverviewData({
        totalEarning: 526,
        totalEarningChange: 3.2,
        inProgress: 12,
        inProgressChange: -1.5,
        waitingList: 8,
        waitingListChange: 2.1,
      });

      setInProgressOrders([
        { id: '1', tableId: 'A9', customerName: 'Adam Hamzah', itemCount: 8, status: 'Ready to serve' },
        { id: '2', tableId: 'A5', customerName: 'Nina Renard', itemCount: 4, status: 'Cooking Now' },
        { id: '3', tableId: 'A3', customerName: 'John Smith', itemCount: 6, status: 'In the Kitchen' },
      ]);

      setWaitingPaymentOrders([
        { id: '6', tableId: 'A13', customerName: 'David Owens', itemCount: 5, status: 'Waiting Payment', totalAmount: 125.50 },
        { id: '7', tableId: 'A15', customerName: 'Olivia Mason', itemCount: 4, status: 'Waiting Payment', totalAmount: 89.75 },
      ]);
    } finally {
      setLoading(false);
    }
  };

  const getDisplayStatus = (status: string) => {
    switch (status) {
      case 'PREPARING':
        return 'Cooking Now';
      case 'READY':
        return 'Ready to serve';
      case 'SERVING':
        return 'In the Kitchen';
      default:
        return status;
    }
  };

  const handleLogout = () => {
    localStorage.removeItem('access_token');
    window.location.href = '/login';
  };

  const getCurrentTime = () => {
    const now = new Date();
    return now.toLocaleTimeString('tr-TR', {
      hour: '2-digit',
      minute: '2-digit',
      hour12: false
    });
  };

  const getCurrentDate = () => {
    const now = new Date();
    return now.toLocaleDateString('tr-TR', {
      weekday: 'long',
      year: 'numeric',
      month: 'long',
      day: 'numeric'
    });
  };

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ready to serve':
        return 'text-green-500';
      case 'Cooking Now':
        return 'text-orange-500';
      case 'In the Kitchen':
        return 'text-blue-500';
      default:
        return 'text-gray-500';
    }
  };

  const getStatusDot = (status: string) => {
    switch (status) {
      case 'Ready to serve':
        return 'bg-green-500';
      case 'Cooking Now':
        return 'bg-orange-500';
      case 'In the Kitchen':
        return 'bg-blue-500';
      default:
        return 'bg-gray-500';
    }
  };

  const filteredInProgressOrders = inProgressOrders.filter(order =>
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredWaitingPaymentOrders = waitingPaymentOrders.filter(order =>
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  return (
    <div className="w-full h-screen bg-gray-50 text-gray-900 flex flex-col overflow-hidden">
      {/* Header */}
      <div className="bg-white shadow-sm border-b border-gray-200 px-6 py-4">
        <div className="flex justify-between items-center">
          {/* Left side - Brand and Search */}
          <div className="flex items-center space-x-8">
            <div>
              <h1 className="text-xl font-bold text-gray-900">Aleo Resto</h1>
              <p className="text-sm text-gray-500">Merkez Şube</p>
            </div>

            {/* Search Bar */}
            <div className="relative">
              <input
                type="text"
                placeholder="Sipariş ara..."
                value={searchTerm}
                onChange={(e) => setSearchTerm(e.target.value)}
                className="w-80 px-4 py-2 pl-10 border border-gray-300 rounded-lg focus:outline-none focus:ring-2 focus:ring-blue-500 focus:border-transparent"
              />
              <div className="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                <svg className="h-5 w-5 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                </svg>
              </div>
            </div>
          </div>

          {/* Right side - Notifications and User */}
          <div className="flex items-center space-x-4">
            {/* Notification */}
            <div className="relative">
              <button className="p-2 text-gray-400 hover:text-gray-600">
                <svg className="h-6 w-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M15 17h5l-5 5v-5z" />
                  <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 7h6a2 2 0 012 2v9a2 2 0 01-2 2H9a2 2 0 01-2-2V9a2 2 0 012-2z" />
                </svg>
              </button>
              <span className="absolute -top-1 -right-1 bg-red-500 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">3</span>
            </div>

            {/* User Info */}
            <div className="flex items-center space-x-3">
              <div className="text-right">
                <p className="text-sm font-medium text-gray-900">Admin User</p>
                <p className="text-xs text-gray-500">Yönetici</p>
              </div>
              <button
                onClick={handleLogout}
                className="bg-red-600 hover:bg-red-700 text-white px-3 py-1 rounded-md text-sm transition-colors"
              >
                Çıkış
              </button>
            </div>
          </div>
        </div>

        {/* Greeting and Date */}
        <div className="mt-4">
          <h2 className="text-lg font-semibold text-gray-900">Good Morning, Admin User</h2>
          <p className="text-sm text-gray-500">{getCurrentDate()} - {getCurrentTime()}</p>
        </div>
      </div>

      {/* Main Content */}
      <div className="flex-1 overflow-y-auto p-6">
        <div className="max-w-7xl mx-auto">
          {error && (
            <div className="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-6">
              Error: {error}
            </div>
          )}

          {loading ? (
            <div className="flex justify-center items-center h-64">
              <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600"></div>
            </div>
          ) : (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-6">
              {/* Left side - Overview Cards */}
              <div className="lg:col-span-2 space-y-6">
                {/* Overview Cards */}
                <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
                  {/* Total Earning Card */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-1">Total Earning</p>
                        <p className="text-3xl font-bold text-gray-900 mb-1">${overviewData?.totalEarning}</p>
                        <p className={`text-sm font-medium ${overviewData?.totalEarningChange && overviewData.totalEarningChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {overviewData?.totalEarningChange && overviewData.totalEarningChange > 0 ? '↗ +' : '↘ '}{Math.abs(overviewData?.totalEarningChange || 0)}% than yesterday
                        </p>
                      </div>
                      <div className="h-14 w-14 bg-gradient-to-br from-green-400 to-green-600 rounded-xl flex items-center justify-center shadow-lg">
                        <svg className="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* In Progress Card */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-1">In Progress</p>
                        <p className="text-3xl font-bold text-gray-900 mb-1">{overviewData?.inProgress}</p>
                        <p className={`text-sm font-medium ${overviewData?.inProgressChange && overviewData.inProgressChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {overviewData?.inProgressChange && overviewData.inProgressChange > 0 ? '↗ +' : '↘ '}{Math.abs(overviewData?.inProgressChange || 0)}% than yesterday
                        </p>
                      </div>
                      <div className="h-14 w-14 bg-gradient-to-br from-orange-400 to-orange-600 rounded-xl flex items-center justify-center shadow-lg">
                        <svg className="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M12 8v4l3 3m6-3a9 9 0 11-18 0 9 9 0 0118 0z" />
                        </svg>
                      </div>
                    </div>
                  </div>

                  {/* Waiting List Card */}
                  <div className="bg-white rounded-xl shadow-sm border border-gray-200 p-6 hover:shadow-md transition-shadow">
                    <div className="flex items-center justify-between">
                      <div>
                        <p className="text-sm font-medium text-gray-500 mb-1">Waiting List</p>
                        <p className="text-3xl font-bold text-gray-900 mb-1">{overviewData?.waitingList}</p>
                        <p className={`text-sm font-medium ${overviewData?.waitingListChange && overviewData.waitingListChange > 0 ? 'text-green-600' : 'text-red-600'}`}>
                          {overviewData?.waitingListChange && overviewData.waitingListChange > 0 ? '↗ +' : '↘ '}{Math.abs(overviewData?.waitingListChange || 0)}% than yesterday
                        </p>
                      </div>
                      <div className="h-14 w-14 bg-gradient-to-br from-blue-400 to-blue-600 rounded-xl flex items-center justify-center shadow-lg">
                        <svg className="h-7 w-7 text-white" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                          <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2" />
                        </svg>
                      </div>
                    </div>
                  </div>
                </div>
              </div>

              {/* Right side - Order Lists */}
              <div className="lg:col-span-1">
                <div className="bg-white rounded-xl shadow-sm border border-gray-200">
                  {/* Tab Headers */}
                  <div className="border-b border-gray-200">
                    <nav className="flex">
                      <button
                        onClick={() => setActiveTab('inProgress')}
                        className={`px-6 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'inProgress'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        In Progress ({filteredInProgressOrders.length})
                      </button>
                      <button
                        onClick={() => setActiveTab('waitingPayment')}
                        className={`px-6 py-3 text-sm font-medium border-b-2 ${
                          activeTab === 'waitingPayment'
                            ? 'border-blue-500 text-blue-600'
                            : 'border-transparent text-gray-500 hover:text-gray-700'
                        }`}
                      >
                        Waiting for Payment ({filteredWaitingPaymentOrders.length})
                      </button>
                    </nav>
                  </div>

                  {/* Tab Content */}
                  <div className="p-4">
                    {activeTab === 'inProgress' && (
                      <div className="space-y-3">
                        {filteredInProgressOrders.length === 0 ? (
                          <p className="text-gray-500 text-center py-8">No orders in progress</p>
                        ) : (
                          filteredInProgressOrders.map((order) => (
                            <div key={order.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 hover:border-gray-300 transition-all">
                              <div className="flex justify-between items-start mb-2">
                                <div className="flex items-center space-x-2">
                                  <span className="font-bold text-gray-900 bg-gray-100 px-2 py-1 rounded text-sm">{order.tableId}</span>
                                  <div className={`w-3 h-3 rounded-full ${getStatusDot(order.status)} shadow-sm`}></div>
                                </div>
                                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{order.itemCount} Items</span>
                              </div>
                              <p className="text-sm font-medium text-gray-800 mb-1">{order.customerName}</p>
                              <p className={`text-xs font-medium ${getStatusColor(order.status)}`}>{order.status}</p>
                            </div>
                          ))
                        )}
                      </div>
                    )}

                    {activeTab === 'waitingPayment' && (
                      <div className="space-y-3">
                        {filteredWaitingPaymentOrders.length === 0 ? (
                          <p className="text-gray-500 text-center py-8">No orders waiting for payment</p>
                        ) : (
                          filteredWaitingPaymentOrders.map((order) => (
                            <div key={order.id} className="border border-gray-200 rounded-lg p-4 hover:bg-gray-50 hover:border-gray-300 transition-all">
                              <div className="flex justify-between items-start mb-2">
                                <div className="flex items-center space-x-2">
                                  <span className="font-bold text-gray-900 bg-gray-100 px-2 py-1 rounded text-sm">{order.tableId}</span>
                                </div>
                                <span className="text-xs text-gray-500 bg-gray-100 px-2 py-1 rounded">{order.itemCount} Items</span>
                              </div>
                              <p className="text-sm font-medium text-gray-800 mb-2">{order.customerName}</p>
                              <div className="flex justify-between items-center">
                                <span className="text-lg font-bold text-gray-900">
                                  ${order.totalAmount?.toFixed(2)}
                                </span>
                                <button className="bg-gradient-to-r from-blue-500 to-blue-600 hover:from-blue-600 hover:to-blue-700 text-white px-4 py-2 rounded-lg text-xs font-medium transition-all shadow-sm hover:shadow-md">
                                  Pay Now
                                </button>
                              </div>
                            </div>
                          ))
                        )}
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          )}
        </div>
      </div>
    </div>
  );
}
