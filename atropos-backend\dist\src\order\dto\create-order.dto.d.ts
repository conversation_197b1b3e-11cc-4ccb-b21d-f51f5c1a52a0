import { OrderType } from '@prisma/client';
export declare class CreateOrderItemDto {
    productId: string;
    variantId?: string;
    quantity: number;
    unitPrice: number;
    costPrice?: number;
    discountAmount?: number;
    discountRate?: number;
    taxRate: number;
    note?: string;
    guestName?: string;
    courseNumber?: number;
}
export declare class CreateOrderDto {
    branchId: string;
    orderType: OrderType;
    tableId?: string;
    customerCount?: number;
    customerId?: string;
    customerName?: string;
    customerPhone?: string;
    deliveryAddress?: string;
    deliveryNote?: string;
    waiterId?: string;
    orderNote?: string;
    kitchenNote?: string;
    internalNote?: string;
    estimatedTime?: number;
    onlinePlatformId?: string;
    platformOrderId?: string;
    platformOrderNo?: string;
    items: CreateOrderItemDto[];
}
