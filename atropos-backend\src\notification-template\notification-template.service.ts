// src/notification-template/notification-template.service.ts
import { Injectable, NotFoundException, ConflictException, BadRequestException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateNotificationTemplateDto } from './dto/create-notification-template.dto';
import { UpdateNotificationTemplateDto } from './dto/update-notification-template.dto';
import { NotificationChannel } from '@prisma/client';

@Injectable()
export class NotificationTemplateService {
  constructor(private prisma: PrismaService) {}

  async createNotificationTemplate(data: CreateNotificationTemplateDto) {
    // Şirket mevcut mu kontrol et
    const companyExists = await this.prisma.company.findUnique({
      where: { id: data.companyId, deletedAt: null },
    });
    if (!companyExists) {
      throw new NotFoundException(`Company with ID "${data.companyId}" not found.`);
    }

    // Aynı şirket içinde aynı kodda şablon var mı kontrol et
    const existingTemplate = await this.prisma.notificationTemplate.findUnique({
      where: {
        companyId_code: {
          companyId: data.companyId,
          code: data.code,
        },
      },
    });
    if (existingTemplate) {
      throw new ConflictException(`Notification template with code "${data.code}" already exists for this company.`);
    }

    // SMS kanalı için subject alanı olmamalı
    if (data.channel === NotificationChannel.SMS && data.subject !== undefined) {
      throw new BadRequestException('Subject field should not be provided for SMS channel.');
    }
    // EMAIL kanalı için subject alanı zorunlu olabilir (iş kuralı)
    if (data.channel === NotificationChannel.EMAIL && !data.subject) {
         throw new BadRequestException('Subject field is required for EMAIL channel.');
    }

    return this.prisma.notificationTemplate.create({ data });
  }

  async findAllNotificationTemplates(companyId?: string, channel?: NotificationChannel, active?: boolean) {
    return this.prisma.notificationTemplate.findMany({
      where: {
        companyId: companyId || undefined,
        channel: channel || undefined,
        active: active !== undefined ? active : undefined,
      },
      include: { company: { select: { id: true, name: true } } },
      orderBy: { name: 'asc' },
    });
  }

  async findOneNotificationTemplate(id: string) {
    const template = await this.prisma.notificationTemplate.findUnique({
      where: { id },
      include: { company: { select: { id: true, name: true } } },
    });
    if (!template) {
      throw new NotFoundException(`Notification template with ID "${id}" not found.`);
    }
    return template;
  }

  async updateNotificationTemplate(id: string, data: UpdateNotificationTemplateDto) {
    const existingTemplate = await this.findOneNotificationTemplate(id);

    // Eğer companyId güncelleniyorsa, yeni şirketin mevcut olduğunu doğrula
    if (data.companyId && data.companyId !== existingTemplate.companyId) {
        const companyExists = await this.prisma.company.findUnique({ where: { id: data.companyId, deletedAt: null } });
        if (!companyExists) { throw new NotFoundException(`Company with ID "${data.companyId}" not found.`); }
    }

    // Code güncelleniyorsa unique kontrolü
    if (data.code && data.code !== existingTemplate.code) {
        const existingTemplateByCode = await this.prisma.notificationTemplate.findUnique({
            where: {
                companyId_code: {
                    companyId: data.companyId || existingTemplate.companyId,
                    code: data.code,
                },
            },
        });
        if (existingTemplateByCode && existingTemplateByCode.id !== id) {
            throw new ConflictException(`Notification template with code "${data.code}" already exists for this company.`);
        }
    }

    // Kanal değişiyorsa veya subject güncelleniyorsa kontrol et
    const targetChannel = data.channel || existingTemplate.channel;
    if (targetChannel === NotificationChannel.SMS && data.subject !== undefined) {
         throw new BadRequestException('Subject field should not be provided for SMS channel.');
    }
    if (targetChannel === NotificationChannel.EMAIL && !data.subject && !existingTemplate.subject) {
         throw new BadRequestException('Subject field is required for EMAIL channel.');
    }

    try {
      return await this.prisma.notificationTemplate.update({
        where: { id },
        data,
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Notification template with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeNotificationTemplate(id: string) {
    // Bu şablona bağlı NotificationLog var mı kontrol et
    const logsCount = await this.prisma.notificationLog.count({
        where: { templateId: id }
    });
    if (logsCount > 0) {
        throw new ConflictException(`Notification template with ID "${id}" cannot be deleted because it has ${logsCount} associated logs.`);
    }

    // NotificationTemplate modelinde deletedAt alanı yok, bu nedenle fiziksel silme yapıyoruz.
    try {
      return await this.prisma.notificationTemplate.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Notification template with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
