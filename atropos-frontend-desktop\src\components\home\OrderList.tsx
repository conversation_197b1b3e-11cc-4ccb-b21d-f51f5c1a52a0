import React, { useState } from 'react';

const searchIcon = "http://localhost:3845/assets/17ecfe562400c8146f7d09317c3599c2dae738da.svg";

interface Order {
  id: string;
  tableId: string;
  customerName: string;
  itemCount: number;
  status: string;
  totalAmount?: number;
}

interface OrderListProps {
  inProgressOrders: Order[];
  waitingPaymentOrders: Order[];
  searchTerm: string;
  onSearchChange: (value: string) => void;
}

export default function OrderList({ 
  inProgressOrders, 
  waitingPaymentOrders, 
  searchTerm, 
  onSearchChange 
}: OrderListProps) {
  const [activeTab, setActiveTab] = useState<'inProgress' | 'waitingPayment'>('inProgress');

  const filteredInProgressOrders = inProgressOrders.filter(order =>
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const filteredWaitingPaymentOrders = waitingPaymentOrders.filter(order =>
    order.customerName.toLowerCase().includes(searchTerm.toLowerCase()) ||
    order.tableId.toLowerCase().includes(searchTerm.toLowerCase())
  );

  const getStatusColor = (status: string) => {
    switch (status) {
      case 'Ready to serve':
        return 'bg-success-500';
      case 'Cooking Now':
        return 'bg-warning-600';
      case 'In the Kitchen':
        return 'bg-primary-400';
      default:
        return 'bg-neutral-500';
    }
  };

  const getTableColor = (index: number) => {
    const colors = ['bg-primary-400', 'bg-success-500', 'bg-warning-600', 'bg-error-500'];
    return colors[index % colors.length];
  };

  return (
    <div className="bg-neutral-black rounded-2xl p-5 h-fit">
      {/* Tabs */}
      <div className="bg-neutral-800 rounded-xl p-0 mb-6">
        <div className="flex">
          <button
            onClick={() => setActiveTab('inProgress')}
            className={`flex-1 px-3 py-3 rounded-xl text-title-4 font-inter transition-colors ${
              activeTab === 'inProgress'
                ? 'bg-neutral-700 text-neutral-white shadow-sm'
                : 'bg-neutral-800 text-neutral-400'
            }`}
          >
            In Progress
          </button>
          <button
            onClick={() => setActiveTab('waitingPayment')}
            className={`flex-1 px-3 py-3 rounded-xl text-title-4 font-inter transition-colors ${
              activeTab === 'waitingPayment'
                ? 'bg-neutral-700 text-neutral-white shadow-sm'
                : 'bg-neutral-800 text-neutral-400'
            }`}
          >
            Waiting for Payment
          </button>
        </div>
      </div>

      {/* Search Input */}
      <div className="mb-6">
        <div className="bg-neutral-800 rounded-xl px-4 py-3 flex items-center gap-3">
          <img src={searchIcon} alt="Search" className="w-6 h-6" />
          <input
            type="text"
            placeholder="Search a Order"
            value={searchTerm}
            onChange={(e) => onSearchChange(e.target.value)}
            className="bg-transparent text-body-4 text-neutral-white placeholder-neutral-500 outline-none flex-1 font-inter"
          />
        </div>
      </div>

      {/* Order List Content */}
      <div className="space-y-6">
        {activeTab === 'inProgress' && (
          <>
            {filteredInProgressOrders.length === 0 ? (
              <p className="text-neutral-400 text-center py-8 font-inter">No orders in progress</p>
            ) : (
              filteredInProgressOrders.map((order, index) => (
                <div key={order.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`${getTableColor(index)} rounded-xl w-[51px] h-[51px] flex items-center justify-center`}>
                      <span className="text-title-4 text-neutral-white font-inter font-semibold">
                        {order.tableId}
                      </span>
                    </div>
                    <div>
                      <p className="text-title-4 text-neutral-white font-inter mb-1">
                        {order.customerName}
                      </p>
                      <p className="text-body-5 text-neutral-300 font-inter">
                        {order.itemCount} Items
                      </p>
                    </div>
                  </div>
                  <div className="flex items-center gap-2">
                    <div className={`w-2 h-2 rounded-full ${getStatusColor(order.status)}`}></div>
                    <span className="text-body-5 text-neutral-300 font-inter">
                      {order.status}
                    </span>
                  </div>
                </div>
              ))
            )}
          </>
        )}

        {activeTab === 'waitingPayment' && (
          <>
            {filteredWaitingPaymentOrders.length === 0 ? (
              <p className="text-neutral-400 text-center py-8 font-inter">No orders waiting for payment</p>
            ) : (
              filteredWaitingPaymentOrders.map((order, index) => (
                <div key={order.id} className="flex items-center justify-between">
                  <div className="flex items-center gap-4">
                    <div className={`${getTableColor(index)} rounded-xl w-[51px] h-[51px] flex items-center justify-center`}>
                      <span className="text-title-4 text-neutral-white font-inter font-semibold">
                        {order.tableId}
                      </span>
                    </div>
                    <div>
                      <p className="text-title-4 text-neutral-white font-inter mb-1">
                        {order.customerName}
                      </p>
                      <p className="text-body-5 text-neutral-300 font-inter">
                        {order.itemCount} Items • ${order.totalAmount?.toFixed(2)}
                      </p>
                    </div>
                  </div>
                  <button className="bg-primary-400 hover:bg-primary-500 text-neutral-white px-4 py-2 rounded-xl text-body-4 font-inter transition-colors">
                    Pay Now
                  </button>
                </div>
              ))
            )}
          </>
        )}
      </div>
    </div>
  );
}
