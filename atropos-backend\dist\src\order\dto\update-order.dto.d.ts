import { OrderStatus, PaymentStatus, OrderType } from '@prisma/client';
export declare class UpdateOrderItemDto {
    id?: string;
    productId?: string;
    variantId?: string;
    quantity?: number;
    unitPrice?: number;
    costPrice?: number;
    discountAmount?: number;
    discountRate?: number;
    taxRate?: number;
    note?: string;
    guestName?: string;
    courseNumber?: number;
    status?: OrderStatus;
}
export declare class UpdateOrderDto {
    branchId?: string;
    orderType?: OrderType;
    tableId?: string;
    customerCount?: number;
    customerId?: string;
    customerName?: string;
    customerPhone?: string;
    deliveryAddress?: string;
    deliveryNote?: string;
    waiterId?: string;
    orderNote?: string;
    kitchenNote?: string;
    internalNote?: string;
    estimatedTime?: number;
    onlinePlatformId?: string;
    platformOrderId?: string;
    platformOrderNo?: string;
    status?: OrderStatus;
    paymentStatus?: PaymentStatus;
    cashierId?: string;
    courierId?: string;
    items?: UpdateOrderItemDto[];
}
