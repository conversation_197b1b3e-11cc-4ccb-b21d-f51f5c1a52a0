import { CashMovementService } from './cash-movement.service';
import { CreateCashMovementDto } from './dto/create-cash-movement.dto';
import { UpdateCashMovementDto } from './dto/update-cash-movement.dto';
import { CashMovementType } from '@prisma/client';
export declare class CashMovementController {
    private readonly cashMovementService;
    constructor(cashMovementService: CashMovementService);
    create(createCashMovementDto: CreateCashMovementDto): Promise<{
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import(".prisma/client").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("@prisma/client/runtime/library").Decimal;
        currentBalance: import("@prisma/client/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
    findAll(branchId?: string, userId?: string, type?: CashMovementType, startDate?: string, endDate?: string): Promise<({
        branch: {
            id: string;
            name: string;
        };
        user: {
            id: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import(".prisma/client").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("@prisma/client/runtime/library").Decimal;
        currentBalance: import("@prisma/client/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            id: string;
            name: string;
        };
        user: {
            id: string;
            firstName: string;
            lastName: string;
        };
    } & {
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import(".prisma/client").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("@prisma/client/runtime/library").Decimal;
        currentBalance: import("@prisma/client/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
    update(id: string, updateCashMovementDto: UpdateCashMovementDto): Promise<{
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import(".prisma/client").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("@prisma/client/runtime/library").Decimal;
        currentBalance: import("@prisma/client/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        cashRegisterId: string | null;
        branchId: string;
        description: string;
        type: import(".prisma/client").$Enums.CashMovementType;
        paymentMethodId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal;
        userId: string;
        referenceId: string | null;
        referenceType: string | null;
        previousBalance: import("@prisma/client/runtime/library").Decimal;
        currentBalance: import("@prisma/client/runtime/library").Decimal;
        safeId: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
    }>;
}
