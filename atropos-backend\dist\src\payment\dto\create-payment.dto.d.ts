import { PaymentStatus } from '@prisma/client';
export declare class CreatePaymentDto {
    orderId: string;
    paymentMethodId: string;
    amount: number;
    tipAmount?: number;
    changeAmount?: number;
    approvalCode?: string;
    referenceNo?: string;
    maskedCardNumber?: string;
    cardHolderName?: string;
    installments?: number;
    transactionId?: string;
    status?: PaymentStatus;
}
