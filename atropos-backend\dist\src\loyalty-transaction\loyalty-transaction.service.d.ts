import { PrismaService } from '../prisma/prisma.service';
import { CreateLoyaltyTransactionDto } from './dto/create-loyalty-transaction.dto';
import { UpdateLoyaltyTransactionDto } from './dto/update-loyalty-transaction.dto';
import { LoyaltyTransactionType } from '@prisma/client';
import { LoyaltyCardService } from '../loyalty-card/loyalty-card.service';
export declare class LoyaltyTransactionService {
    private prisma;
    private loyaltyCardService;
    constructor(prisma: PrismaService, loyaltyCardService: LoyaltyCardService);
    createLoyaltyTransaction(data: CreateLoyaltyTransactionDto): Promise<{
        order: {
            id: string;
            createdAt: Date;
            updatedAt: Date;
            deletedAt: Date | null;
            branchId: string;
            version: number;
            syncId: string | null;
            lastSyncAt: Date | null;
            status: import(".prisma/client").$Enums.OrderStatus;
            completedAt: Date | null;
            orderNumber: string;
            orderCode: string | null;
            orderType: import(".prisma/client").$Enums.OrderType;
            tableId: string | null;
            customerCount: number | null;
            customerId: string | null;
            customerName: string | null;
            customerPhone: string | null;
            deliveryAddress: string | null;
            deliveryNote: string | null;
            paymentStatus: import(".prisma/client").$Enums.PaymentStatus;
            mergeTargetId: string | null;
            splitFromId: string | null;
            subtotal: import("@prisma/client/runtime/library").Decimal;
            discountAmount: import("@prisma/client/runtime/library").Decimal;
            discountRate: import("@prisma/client/runtime/library").Decimal;
            discountReason: string | null;
            serviceCharge: import("@prisma/client/runtime/library").Decimal;
            deliveryFee: import("@prisma/client/runtime/library").Decimal;
            taxAmount: import("@prisma/client/runtime/library").Decimal;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
            paidAmount: import("@prisma/client/runtime/library").Decimal;
            changeAmount: import("@prisma/client/runtime/library").Decimal;
            tipAmount: import("@prisma/client/runtime/library").Decimal;
            roundingAmount: import("@prisma/client/runtime/library").Decimal;
            waiterId: string | null;
            cashierId: string | null;
            courierId: string | null;
            orderNote: string | null;
            kitchenNote: string | null;
            internalNote: string | null;
            orderedAt: Date;
            confirmedAt: Date | null;
            preparingAt: Date | null;
            preparedAt: Date | null;
            servedAt: Date | null;
            deliveredAt: Date | null;
            cancelledAt: Date | null;
            estimatedTime: number | null;
            actualTime: number | null;
            onlinePlatformId: string | null;
            platformOrderId: string | null;
            platformOrderNo: string | null;
        } | null;
        card: {
            id: string;
            active: boolean;
            pin: string | null;
            customerId: string;
            discountRate: import("@prisma/client/runtime/library").Decimal;
            cardNumber: string;
            cardType: import(".prisma/client").$Enums.LoyaltyCardType;
            points: number;
            totalEarnedPoints: number;
            totalSpentPoints: number;
            balance: import("@prisma/client/runtime/library").Decimal;
            totalLoaded: import("@prisma/client/runtime/library").Decimal;
            issuedAt: Date;
            activatedAt: Date | null;
            expiresAt: Date | null;
            blocked: boolean;
            blockReason: string | null;
            lastUsedAt: Date | null;
        };
    } & {
        id: string;
        createdAt: Date;
        description: string;
        type: import(".prisma/client").$Enums.LoyaltyTransactionType;
        orderId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal | null;
        createdBy: string | null;
        points: number;
        expiresAt: Date | null;
        cardId: string;
        baseAmount: import("@prisma/client/runtime/library").Decimal | null;
        multiplier: import("@prisma/client/runtime/library").Decimal | null;
        pointBalance: number;
        moneyBalance: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    findAllLoyaltyTransactions(cardId?: string, orderId?: string, type?: LoyaltyTransactionType, createdBy?: string, startDate?: Date, endDate?: Date): Promise<({
        order: {
            id: string;
            orderNumber: string;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
        } | null;
        card: {
            id: string;
            customerId: string;
            cardNumber: string;
            cardType: import(".prisma/client").$Enums.LoyaltyCardType;
        };
    } & {
        id: string;
        createdAt: Date;
        description: string;
        type: import(".prisma/client").$Enums.LoyaltyTransactionType;
        orderId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal | null;
        createdBy: string | null;
        points: number;
        expiresAt: Date | null;
        cardId: string;
        baseAmount: import("@prisma/client/runtime/library").Decimal | null;
        multiplier: import("@prisma/client/runtime/library").Decimal | null;
        pointBalance: number;
        moneyBalance: import("@prisma/client/runtime/library").Decimal | null;
    })[]>;
    findOneLoyaltyTransaction(id: string): Promise<{
        order: {
            id: string;
            orderNumber: string;
            totalAmount: import("@prisma/client/runtime/library").Decimal;
        } | null;
        card: {
            id: string;
            customerId: string;
            cardNumber: string;
            cardType: import(".prisma/client").$Enums.LoyaltyCardType;
        };
    } & {
        id: string;
        createdAt: Date;
        description: string;
        type: import(".prisma/client").$Enums.LoyaltyTransactionType;
        orderId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal | null;
        createdBy: string | null;
        points: number;
        expiresAt: Date | null;
        cardId: string;
        baseAmount: import("@prisma/client/runtime/library").Decimal | null;
        multiplier: import("@prisma/client/runtime/library").Decimal | null;
        pointBalance: number;
        moneyBalance: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    updateLoyaltyTransaction(id: string, data: UpdateLoyaltyTransactionDto): Promise<{
        id: string;
        createdAt: Date;
        description: string;
        type: import(".prisma/client").$Enums.LoyaltyTransactionType;
        orderId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal | null;
        createdBy: string | null;
        points: number;
        expiresAt: Date | null;
        cardId: string;
        baseAmount: import("@prisma/client/runtime/library").Decimal | null;
        multiplier: import("@prisma/client/runtime/library").Decimal | null;
        pointBalance: number;
        moneyBalance: import("@prisma/client/runtime/library").Decimal | null;
    }>;
    removeLoyaltyTransaction(id: string): Promise<{
        id: string;
        createdAt: Date;
        description: string;
        type: import(".prisma/client").$Enums.LoyaltyTransactionType;
        orderId: string | null;
        amount: import("@prisma/client/runtime/library").Decimal | null;
        createdBy: string | null;
        points: number;
        expiresAt: Date | null;
        cardId: string;
        baseAmount: import("@prisma/client/runtime/library").Decimal | null;
        multiplier: import("@prisma/client/runtime/library").Decimal | null;
        pointBalance: number;
        moneyBalance: import("@prisma/client/runtime/library").Decimal | null;
    }>;
}
