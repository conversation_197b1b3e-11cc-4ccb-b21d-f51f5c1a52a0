import { CampaignType, DiscountType } from '@prisma/client';
export declare class CreateCampaignDto {
    companyId: string;
    name: string;
    code: string;
    description?: string;
    campaignType: CampaignType;
    discountType?: DiscountType;
    discountValue?: number;
    minOrderAmount?: number;
    maxDiscountAmount?: number;
    startDate: Date;
    endDate?: Date;
    usageLimit?: number;
    usageLimitPerUser?: number;
    active?: boolean;
}
