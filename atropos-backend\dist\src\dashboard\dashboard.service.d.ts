import { PrismaService } from '../prisma/prisma.service';
export declare class DashboardService {
    private prisma;
    constructor(prisma: PrismaService);
    getOverview(branchId?: string): Promise<{
        totalEarning: number;
        totalEarningChange: number;
        inProgress: number;
        inProgressChange: number;
        waitingList: number;
        waitingListChange: number;
    }>;
}
