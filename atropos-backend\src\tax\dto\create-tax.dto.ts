// src/tax/dto/create-tax.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsBoolean,
  IsDecimal,
  Min,
  Max,
  IsEnum,
} from 'class-validator';
import { TaxType } from '@prisma/client'; // Prisma'dan TaxType enum'ını import et
import { Type } from 'class-transformer';

export class CreateTaxDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  name: string; // "KDV %8"

  @Type(() => Number) // Gelen string değeri number'a dönüştür
  @Min(0)
  @Max(100)
  @IsNotEmpty()
  rate: number; // Örn: 8.00 veya 18.00

  @IsString()
  @IsNotEmpty()
  code: string; // "VAT8"

  @IsEnum(TaxType)
  @IsNotEmpty()
  type: TaxType; // VAT, OTV, OIV, DAMGA

  @IsBoolean()
  @IsNotEmpty()
  isDefault: boolean; // Varsayılan vergi oranı mı?

  @IsBoolean()
  @IsNotEmpty()
  isIncluded: boolean; // Fiyata dahil mi?

  @IsBoolean()
  @IsNotEmpty()
  active: boolean;
}
