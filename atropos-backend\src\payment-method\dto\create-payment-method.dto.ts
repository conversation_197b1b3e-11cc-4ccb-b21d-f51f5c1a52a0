// src/payment-method/dto/create-payment-method.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsBoolean,
  IsInt,
  Min,
  Max,
  <PERSON>Number,
} from 'class-validator';
import { Type } from 'class-transformer';
import { PaymentMethodType } from '@prisma/client'; // Prisma'dan PaymentMethodType enum'ını import et

export class CreatePaymentMethodDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  name: string; // "Nakit", "<PERSON><PERSON><PERSON> Ka<PERSON>"

  @IsString()
  @IsNotEmpty()
  code: string; // "CASH", "CC"

  @IsEnum(PaymentMethodType)
  @IsNotEmpty()
  type: PaymentMethodType;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @Max(100)
  @IsOptional()
  commissionRate?: number; // <PERSON><PERSON><PERSON><PERSON> olarak

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  minAmount?: number;

  @Type(() => Number)
  @IsNumber({ maxDecimalPlaces: 2 })
  @Min(0)
  @IsOptional()
  maxAmount?: number;

  @IsBoolean()
  @IsOptional()
  requiresApproval?: boolean;

  @IsBoolean()
  @IsOptional()
  requiresReference?: boolean;

  @IsString()
  @IsOptional()
  providerName?: string; // "Garanti", "YKB"

  @IsString()
  @IsOptional()
  merchantId?: string;

  @IsString()
  @IsOptional()
  terminalId?: string;

  @IsInt()
  @IsOptional()
  @Min(0)
  displayOrder?: number;

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
