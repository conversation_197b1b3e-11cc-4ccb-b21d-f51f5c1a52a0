import { PrismaService } from '../prisma/prisma.service';
import { CreateNotificationLogDto } from './dto/create-notification-log.dto';
import { UpdateNotificationLogDto } from './dto/update-notification-log.dto';
import { NotificationStatus } from '@prisma/client';
export declare class NotificationLogService {
    private prisma;
    constructor(prisma: PrismaService);
    createNotificationLog(data: CreateNotificationLogDto): Promise<{
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
    findAllNotificationLogs(templateId?: string, recipient?: string, channel?: string, status?: NotificationStatus, startDate?: Date, endDate?: Date): Promise<({
        template: {
            id: string;
            name: string;
            code: string;
            channel: import(".prisma/client").$Enums.NotificationChannel;
        };
    } & {
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    })[]>;
    findOneNotificationLog(id: string): Promise<{
        template: {
            id: string;
            name: string;
            code: string;
            channel: import(".prisma/client").$Enums.NotificationChannel;
        };
    } & {
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
    updateNotificationLog(id: string, data: UpdateNotificationLogDto): Promise<{
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
    removeNotificationLog(id: string): Promise<{
        id: string;
        status: import(".prisma/client").$Enums.NotificationStatus;
        deliveredAt: Date | null;
        sentAt: Date;
        channel: import(".prisma/client").$Enums.NotificationChannel;
        templateId: string;
        recipient: string;
        message: string;
        response: import("@prisma/client/runtime/library").JsonValue | null;
        failedReason: string | null;
        readAt: Date | null;
    }>;
}
