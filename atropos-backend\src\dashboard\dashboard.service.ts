// src/dashboard/dashboard.service.ts
import { Injectable } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { OrderStatus, PaymentStatus } from '@prisma/client';

@Injectable()
export class DashboardService {
  constructor(private prisma: PrismaService) {}

  async getOverview(branchId?: string) {
    const today = new Date();
    const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
    const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
    
    const yesterday = new Date(today);
    yesterday.setDate(yesterday.getDate() - 1);
    const startOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
    const endOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);

    // Base where condition
    const baseWhere: any = {
      deletedAt: null,
    };
    
    if (branchId) {
      baseWhere.branchId = branchId;
    }

    // Today's orders
    const todayOrders = await this.prisma.order.findMany({
      where: {
        ...baseWhere,
        createdAt: { gte: startOfDay, lte: endOfDay },
      },
      include: {
        payments: {
          where: { deletedAt: null, status: { in: ['PAID', 'PARTIALLY_PAID'] } },
        },
      },
    });

    // Yesterday's orders for comparison
    const yesterdayOrders = await this.prisma.order.findMany({
      where: {
        ...baseWhere,
        createdAt: { gte: startOfYesterday, lte: endOfYesterday },
      },
      include: {
        payments: {
          where: { deletedAt: null, status: { in: ['PAID', 'PARTIALLY_PAID'] } },
        },
      },
    });

    // Calculate today's total earning
    const todayTotalEarning = todayOrders.reduce((total, order) => {
      const paidAmount = order.payments.reduce((sum, payment) => sum + payment.amount.toNumber(), 0);
      return total + paidAmount;
    }, 0);

    // Calculate yesterday's total earning
    const yesterdayTotalEarning = yesterdayOrders.reduce((total, order) => {
      const paidAmount = order.payments.reduce((sum, payment) => sum + payment.amount.toNumber(), 0);
      return total + paidAmount;
    }, 0);

    // Calculate earning change percentage
    const earningChange = yesterdayTotalEarning > 0 
      ? ((todayTotalEarning - yesterdayTotalEarning) / yesterdayTotalEarning) * 100 
      : 0;

    // In progress orders (PREPARING, READY, SERVING)
    const inProgressOrders = await this.prisma.order.count({
      where: {
        ...baseWhere,
        status: { in: [OrderStatus.PREPARING, OrderStatus.READY, OrderStatus.SERVING] },
      },
    });

    // Yesterday's in progress count for comparison
    const yesterdayInProgress = await this.prisma.order.count({
      where: {
        ...baseWhere,
        createdAt: { gte: startOfYesterday, lte: endOfYesterday },
        status: { in: [OrderStatus.PREPARING, OrderStatus.READY, OrderStatus.SERVING] },
      },
    });

    const inProgressChange = yesterdayInProgress > 0 
      ? ((inProgressOrders - yesterdayInProgress) / yesterdayInProgress) * 100 
      : 0;

    // Waiting for payment orders
    const waitingPaymentOrders = await this.prisma.order.count({
      where: {
        ...baseWhere,
        paymentStatus: { in: [PaymentStatus.UNPAID, PaymentStatus.PENDING] },
        status: { not: OrderStatus.CANCELLED },
      },
    });

    // Yesterday's waiting payment count for comparison
    const yesterdayWaitingPayment = await this.prisma.order.count({
      where: {
        ...baseWhere,
        createdAt: { gte: startOfYesterday, lte: endOfYesterday },
        paymentStatus: { in: [PaymentStatus.UNPAID, PaymentStatus.PENDING] },
        status: { not: OrderStatus.CANCELLED },
      },
    });

    const waitingPaymentChange = yesterdayWaitingPayment > 0 
      ? ((waitingPaymentOrders - yesterdayWaitingPayment) / yesterdayWaitingPayment) * 100 
      : 0;

    return {
      totalEarning: parseFloat(todayTotalEarning.toFixed(2)),
      totalEarningChange: parseFloat(earningChange.toFixed(1)),
      inProgress: inProgressOrders,
      inProgressChange: parseFloat(inProgressChange.toFixed(1)),
      waitingList: waitingPaymentOrders,
      waitingListChange: parseFloat(waitingPaymentChange.toFixed(1)),
    };
  }
}
