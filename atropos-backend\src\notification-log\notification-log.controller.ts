// src/notification-log/notification-log.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { NotificationLogService } from './notification-log.service';
import { CreateNotificationLogDto } from './dto/create-notification-log.dto';
import { UpdateNotificationLogDto } from './dto/update-notification-log.dto';
import { NotificationChannel, NotificationStatus } from '@prisma/client';
import { ParseOptionalEnumPipe } from '../common/pipes/parse-optional-enum.pipe';
import { ParseOptionalDatePipe } from '../common/pipes/parse-optional-date.pipe'; // Tarih <PERSON>'ı

@Controller('notification-log')
export class NotificationLogController {
  constructor(private readonly notificationLogService: NotificationLogService) {}

  @Post() // POST /notification-log
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createNotificationLogDto: CreateNotificationLogDto) {
    return this.notificationLogService.createNotificationLog(createNotificationLogDto);
  }

  @Get() // GET /notification-log?templateId=...&recipient=...&channel=...&status=...&startDate=...&endDate=...
  findAll(
    @Query('templateId') templateId?: string,
    @Query('recipient') recipient?: string,
    @Query('channel', new ParseOptionalEnumPipe(NotificationChannel)) channel?: NotificationChannel,
    @Query('status', new ParseOptionalEnumPipe(NotificationStatus)) status?: NotificationStatus,
    @Query('startDate', ParseOptionalDatePipe) startDate?: Date,
    @Query('endDate', ParseOptionalDatePipe) endDate?: Date,
  ) {
    return this.notificationLogService.findAllNotificationLogs(
      templateId,
      recipient,
      channel,
      status,
      startDate,
      endDate,
    );
  }

  @Get(':id') // GET /notification-log/:id
  findOne(@Param('id') id: string) {
    return this.notificationLogService.findOneNotificationLog(id);
  }

  @Patch(':id') // PATCH /notification-log/:id
  update(@Param('id') id: string, @Body() updateNotificationLogDto: UpdateNotificationLogDto) {
    return this.notificationLogService.updateNotificationLog(id, updateNotificationLogDto);
  }

  @Delete(':id') // DELETE /notification-log/:id (Fiziksel silme)
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.notificationLogService.removeNotificationLog(id);
  }
}
