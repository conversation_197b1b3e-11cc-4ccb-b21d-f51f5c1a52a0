
import { useState, useEffect } from 'react';
import BackgroundSection from './BackgroundSection';
import LoginForm from './LoginForm';

interface Employee {
  id: string;
  firstName: string;
  lastName: string;
  username: string;
  role: string;
  avatar?: string;
  workingHours?: string;
}

export default function LoginPage() {
  const [loading, setLoading] = useState<boolean>(false);
  const [error, setError] = useState<string | null>(null);
  const [employees, setEmployees] = useState<Employee[]>([]);

  const API_URL = import.meta.env.VITE_API_URL || 'http://localhost:3000';

  useEffect(() => {
    fetchEmployees();
  }, []);

  const fetchEmployees = async () => {
    try {
      const response = await fetch(`${API_URL}/user/employees`);
      if (!response.ok) {
        throw new Error('Failed to fetch employees');
      }
      const data: Employee[] = await response.json();
      setEmployees(data);
    } catch (err: any) {
      console.error('Error fetching employees:', err);
      setError(err.message);
    }
  };

  const handlePinLogin = async (userId: string, pin: string) => {
    if (!userId || !pin) {
      setError('Please select an employee and enter PIN');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/auth/login-pin`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          userId: userId,
          pin: pin,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const data = await response.json();
      localStorage.setItem('access_token', data.access_token);

      // Ana sayfaya yönlendir
      window.location.href = '/home';
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  const handleNormalLogin = async (username: string, password: string) => {
    if (!username || !password) {
      setError('Please enter username and password');
      return;
    }

    setLoading(true);
    setError(null);

    try {
      const response = await fetch(`${API_URL}/auth/login`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          username: username,
          password: password,
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.message || 'Login failed');
      }

      const data = await response.json();
      localStorage.setItem('access_token', data.access_token);

      // Ana sayfaya yönlendir
      window.location.href = '/home';
    } catch (err: any) {
      setError(err.message);
    } finally {
      setLoading(false);
    }
  };

  return (
    <div className="bg-white relative w-full h-screen flex">
      <BackgroundSection />
      <LoginForm
        employees={employees}
        loading={loading}
        error={error}
        onPinLogin={handlePinLogin}
        onNormalLogin={handleNormalLogin}
      />
    </div>
  );
}
