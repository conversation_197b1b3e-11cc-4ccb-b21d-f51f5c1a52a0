"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.CashMovementService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let CashMovementService = class CashMovementService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async createCashMovement(data) {
        const branchExists = await this.prisma.branch.findUnique({
            where: { id: data.branchId, deletedAt: null },
        });
        if (!branchExists) {
            throw new common_1.NotFoundException(`Branch with ID "${data.branchId}" not found.`);
        }
        const userExists = await this.prisma.user.findUnique({
            where: { id: data.userId, deletedAt: null },
        });
        if (!userExists) {
            throw new common_1.NotFoundException(`User with ID "${data.userId}" not found.`);
        }
        if (data.paymentMethodId) {
            const paymentMethodExists = await this.prisma.paymentMethod.findUnique({
                where: { id: data.paymentMethodId, deletedAt: null }
            });
            if (!paymentMethodExists) {
                throw new common_1.NotFoundException(`Payment Method with ID "${data.paymentMethodId}" not found.`);
            }
            if (paymentMethodExists.type !== 'CASH') {
                throw new common_1.BadRequestException(`Only 'CASH' type payment methods are allowed for cash movements.`);
            }
        }
        const latestMovement = await this.prisma.cashMovement.findFirst({
            where: { branchId: data.branchId },
            orderBy: { createdAt: 'desc' },
        });
        const previousBalance = latestMovement ? latestMovement.currentBalance.toNumber() : 0;
        const currentBalance = previousBalance + data.amount;
        if (currentBalance < 0 && (data.type === client_1.CashMovementType.WITHDRAWAL || data.type === client_1.CashMovementType.EXPENSE)) {
        }
        const cashMovement = await this.prisma.cashMovement.create({
            data: {
                ...data,
                amount: parseFloat(data.amount.toFixed(2)),
                previousBalance: parseFloat(previousBalance.toFixed(2)),
                currentBalance: parseFloat(currentBalance.toFixed(2)),
            },
        });
        return cashMovement;
    }
    async findAllCashMovements(branchId, userId, type, startDate, endDate) {
        return this.prisma.cashMovement.findMany({
            where: {
                branchId: branchId || undefined,
                userId: userId || undefined,
                type: type || undefined,
                createdAt: {
                    gte: startDate || undefined,
                    lte: endDate || undefined,
                },
            },
            include: {
                branch: { select: { id: true, name: true } },
                user: { select: { id: true, firstName: true, lastName: true } },
            },
            orderBy: { createdAt: 'desc' },
        });
    }
    async findOneCashMovement(id) {
        const movement = await this.prisma.cashMovement.findUnique({
            where: { id },
            include: {
                branch: { select: { id: true, name: true } },
                user: { select: { id: true, firstName: true, lastName: true } },
            },
        });
        if (!movement) {
            throw new common_1.NotFoundException(`Cash movement with ID "${id}" not found.`);
        }
        return movement;
    }
    async updateCashMovement(id, data) {
        const existingMovement = await this.findOneCashMovement(id);
        if (data.amount !== undefined || data.type !== undefined || data.branchId !== undefined || data.userId !== undefined || data.paymentMethodId !== undefined || data.referenceId !== undefined || data.referenceType !== undefined) {
            throw new common_1.ForbiddenException('Only "approvedBy" and "approvedAt" fields can be updated for cash movements. For other changes, consider voiding and re-creating the movement.');
        }
        try {
            return await this.prisma.cashMovement.update({
                where: { id },
                data,
            });
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Cash movement with ID "${id}" not found.`);
            }
            throw error;
        }
    }
    async removeCashMovement(id) {
        try {
            const movement = await this.prisma.cashMovement.delete({
                where: { id },
            });
            return movement;
        }
        catch (error) {
            if (error.code === 'P2025') {
                throw new common_1.NotFoundException(`Cash movement with ID "${id}" not found.`);
            }
            throw error;
        }
    }
};
exports.CashMovementService = CashMovementService;
exports.CashMovementService = CashMovementService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], CashMovementService);
//# sourceMappingURL=cash-movement.service.js.map