// src/notification-log/notification-log.service.ts
import { Injectable, NotFoundException, BadRequestException, ForbiddenException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreateNotificationLogDto } from './dto/create-notification-log.dto';
import { UpdateNotificationLogDto } from './dto/update-notification-log.dto';
import { NotificationStatus } from '@prisma/client';

@Injectable()
export class NotificationLogService {
  constructor(private prisma: PrismaService) {}

  async createNotificationLog(data: CreateNotificationLogDto) {
    // NotificationTemplate mevcut mu kontrol et
    const templateExists = await this.prisma.notificationTemplate.findUnique({
      where: { id: data.templateId },
    });
    if (!templateExists) {
      throw new NotFoundException(`Notification template with ID "${data.templateId}" not found.`);
    }

    return this.prisma.notificationLog.create({
      data: {
        ...data,
        status: data.status || NotificationStatus.PENDING, // Varsayılan durum
        sentAt: new Date(), // <PERSON><PERSON><PERSON><PERSON> zamanı
      },
    });
  }

  async findAllNotificationLogs(templateId?: string, recipient?: string, channel?: string, status?: NotificationStatus, startDate?: Date, endDate?: Date) {
    return this.prisma.notificationLog.findMany({
      where: {
        templateId: templateId || undefined,
        recipient: recipient || undefined,
        channel: channel as any || undefined, // Type casting for enum filter
        status: status || undefined,
        sentAt: {
          gte: startDate || undefined,
          lte: endDate ? new Date(endDate.getTime() + (24 * 60 * 60 * 1000) - 1) : undefined, // Gün sonunu dahil et
        },
      },
      include: {
        template: { select: { id: true, name: true, code: true, channel: true } },
      },
      orderBy: { sentAt: 'desc' },
    });
  }

  async findOneNotificationLog(id: string) {
    const log = await this.prisma.notificationLog.findUnique({
      where: { id },
      include: {
        template: { select: { id: true, name: true, code: true, channel: true } },
      },
    });
    if (!log) {
      throw new NotFoundException(`Notification log with ID "${id}" not found.`);
    }
    return log;
  }

  async updateNotificationLog(id: string, data: UpdateNotificationLogDto) {
    const existingLog = await this.findOneNotificationLog(id);

    // Durum geçişleri için iş kuralları uygulanabilir (örn: DELIVERED'dan SENT'e geri dönülemez)
    // Şimdilik sadece statü güncellenebiliyor.
    // templateId, recipient, channel gibi temel alanların güncellenmesi yasak.
    if (data.templateId !== undefined || data.recipient !== undefined || data.channel !== undefined ||
        data.message !== undefined) {
        throw new ForbiddenException('Cannot update core fields of a notification log. Only status, timestamps, and failedReason can be updated.');
    }

    try {
      return await this.prisma.notificationLog.update({
        where: { id },
        data: {
            status: data.status || undefined,
            sentAt: data.sentAt || undefined,
            deliveredAt: data.deliveredAt || undefined,
            readAt: data.readAt || undefined,
            failedReason: data.failedReason || undefined,
        },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Notification log with ID "${id}" not found.`);
      }
      throw error;
    }
  }

  async removeNotificationLog(id: string) {
    // NotificationLog modelinde deletedAt alanı yok.
    // Log kayıtları genellikle fiziksel olarak silinmez.
    // Bu endpoint'in erişimi çok kısıtlı olmalıdır.
    try {
      return await this.prisma.notificationLog.delete({
        where: { id },
      });
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Notification log with ID "${id}" not found.`);
      }
      throw error;
    }
  }
}
