import { StockMovementService } from './stock-movement.service';
import { CreateStockMovementDto } from './dto/create-stock-movement.dto';
import { UpdateStockMovementDto } from './dto/update-stock-movement.dto';
import { StockMovementType } from '@prisma/client';
export declare class StockMovementController {
    private readonly stockMovementService;
    constructor(stockMovementService: StockMovementService);
    create(createStockMovementDto: CreateStockMovementDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        type: import(".prisma/client").$Enums.StockMovementType;
        unit: import(".prisma/client").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("@prisma/client/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("@prisma/client/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("@prisma/client/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("@prisma/client/runtime/library").Decimal | null;
        previousCost: import("@prisma/client/runtime/library").Decimal | null;
        newAverageCost: import("@prisma/client/runtime/library").Decimal | null;
        previousStock: import("@prisma/client/runtime/library").Decimal;
    }>;
    findAll(branchId?: string, productId?: string, inventoryItemId?: string, type?: StockMovementType, startDate?: string, endDate?: string): Promise<({
        branch: {
            id: string;
            name: string;
        };
        product: {
            id: string;
            name: string;
            code: string;
        } | null;
        inventoryItem: {
            id: string;
            name: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        type: import(".prisma/client").$Enums.StockMovementType;
        unit: import(".prisma/client").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("@prisma/client/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("@prisma/client/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("@prisma/client/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("@prisma/client/runtime/library").Decimal | null;
        previousCost: import("@prisma/client/runtime/library").Decimal | null;
        newAverageCost: import("@prisma/client/runtime/library").Decimal | null;
        previousStock: import("@prisma/client/runtime/library").Decimal;
    })[]>;
    findOne(id: string): Promise<{
        branch: {
            id: string;
            name: string;
        };
        product: {
            id: string;
            name: string;
            code: string;
        } | null;
        inventoryItem: {
            id: string;
            name: string;
            code: string;
        } | null;
    } & {
        id: string;
        createdAt: Date;
        branchId: string;
        type: import(".prisma/client").$Enums.StockMovementType;
        unit: import(".prisma/client").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("@prisma/client/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("@prisma/client/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("@prisma/client/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("@prisma/client/runtime/library").Decimal | null;
        previousCost: import("@prisma/client/runtime/library").Decimal | null;
        newAverageCost: import("@prisma/client/runtime/library").Decimal | null;
        previousStock: import("@prisma/client/runtime/library").Decimal;
    }>;
    update(id: string, updateStockMovementDto: UpdateStockMovementDto): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        type: import(".prisma/client").$Enums.StockMovementType;
        unit: import(".prisma/client").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("@prisma/client/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("@prisma/client/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("@prisma/client/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("@prisma/client/runtime/library").Decimal | null;
        previousCost: import("@prisma/client/runtime/library").Decimal | null;
        newAverageCost: import("@prisma/client/runtime/library").Decimal | null;
        previousStock: import("@prisma/client/runtime/library").Decimal;
    }>;
    remove(id: string): Promise<{
        id: string;
        createdAt: Date;
        branchId: string;
        type: import(".prisma/client").$Enums.StockMovementType;
        unit: import(".prisma/client").$Enums.ProductUnit;
        productId: string | null;
        quantity: import("@prisma/client/runtime/library").Decimal;
        note: string | null;
        referenceNo: string | null;
        referenceId: string | null;
        referenceType: string | null;
        approvedBy: string | null;
        approvedAt: Date | null;
        createdBy: string;
        currentStock: import("@prisma/client/runtime/library").Decimal;
        inventoryItemId: string | null;
        reason: string | null;
        unitCost: import("@prisma/client/runtime/library").Decimal | null;
        fromBranchId: string | null;
        toBranchId: string | null;
        supplierId: string | null;
        invoiceNo: string | null;
        attachments: string[];
        totalCost: import("@prisma/client/runtime/library").Decimal | null;
        previousCost: import("@prisma/client/runtime/library").Decimal | null;
        newAverageCost: import("@prisma/client/runtime/library").Decimal | null;
        previousStock: import("@prisma/client/runtime/library").Decimal;
    }>;
}
