import { PrismaService } from '../prisma/prisma.service';
import { CreateTableMergeDto } from './dto/create-table-merge.dto';
import { UpdateTableMergeDto } from './dto/update-table-merge.dto';
export declare class TableMergeService {
    private prisma;
    constructor(prisma: PrismaService);
    createTableMerge(data: CreateTableMergeDto): Promise<{
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    }>;
    findAllTableMerges(tableId?: string, targetId?: string): Promise<({
        mainTable: {
            number: string;
            id: string;
            name: string | null;
            branchId: string;
            status: import(".prisma/client").$Enums.TableStatus;
        };
        mergedTable: {
            number: string;
            id: string;
            name: string | null;
            branchId: string;
            status: import(".prisma/client").$Enums.TableStatus;
        };
    } & {
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    })[]>;
    findOneTableMerge(id: string): Promise<{
        mainTable: {
            number: string;
            id: string;
            name: string | null;
            branchId: string;
            status: import(".prisma/client").$Enums.TableStatus;
        };
        mergedTable: {
            number: string;
            id: string;
            name: string | null;
            branchId: string;
            status: import(".prisma/client").$Enums.TableStatus;
        };
    } & {
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    }>;
    updateTableMerge(id: string, data: UpdateTableMergeDto): Promise<void>;
    removeTableMerge(id: string): Promise<{
        id: string;
        createdAt: Date;
        tableId: string;
        targetId: string;
    }>;
}
