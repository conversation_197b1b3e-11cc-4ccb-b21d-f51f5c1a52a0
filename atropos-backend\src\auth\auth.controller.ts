// src/auth/auth.controller.ts
import { Controller, Post, Request, UseGuards, Get, Body, BadRequestException, UnauthorizedException } from '@nestjs/common';
import { LocalAuthGuard } from './local-auth.guard'; // LocalAuthGuard'ı import et
import { AuthService } from './auth.service';
import { JwtAuthGuard } from './jwt-auth.guard'; // JwtAuthGuard'ı import et
import { UserRole } from '@prisma/client'; // UserRole enum'ını import et
import { Roles } from './roles.decorator'; // Custom decorator'ı import et
import { RolesGuard } from './roles.guard'; // RolesGuard'ı import et

@Controller('auth')
export class AuthController {
  constructor(private authService: AuthService) {}

  @UseGuards(LocalAuthGuard) // Local stratejisi ile kimlik doğrulama
  @Post('login')
  async login(@Request() req) {
    // req.user, LocalAuthGuard tarafından doğrulanmış kullanıcıdır.
    return this.authService.login(req.user);
  }

  @Post('login-pin')
  async loginWithPin(@Body() body: { userId: string; pin: string }) {
    const { userId, pin } = body;

    if (!userId || !pin) {
      throw new BadRequestException('User ID and PIN are required');
    }

    const user = await this.authService.validatePin(userId, pin);
    if (!user) {
      throw new UnauthorizedException('Invalid PIN or user not found');
    }

    return this.authService.loginWithPin(user);
  }

  @UseGuards(JwtAuthGuard) // JWT stratejisi ile kimlik doğrulama
  @Get('profile')
  getProfile(@Request() req) {
    // req.user, JwtAuthGuard tarafından doğrulanmış kullanıcıdır.
    // Hassas bilgileri (şifre gibi) döndürmediğimizden emin olmalıyız.
    const { password, refreshToken, ...userWithoutSensitiveData } = req.user;
    return userWithoutSensitiveData;
  }

  // Rol bazlı yetkilendirme örneği
  @UseGuards(JwtAuthGuard, RolesGuard) // Hem kimlik doğrulama hem yetkilendirme
  @Roles(UserRole.ADMIN) // Sadece ADMIN rolündekiler erişebilir
  @Get('admin-dashboard')
  getAdminDashboard(@Request() req) {
    return `Welcome, Admin ${req.user.firstName}! This is the admin dashboard.`;
  }
}
