// src/order/order.controller.ts
import {
  Controller,
  Get,
  Post,
  Body,
  Patch,
  Param,
  Delete,
  HttpCode,
  HttpStatus,
  Query,
} from '@nestjs/common';
import { OrderService } from './order.service';
import { CreateOrderDto } from './dto/create-order.dto';
import { UpdateOrderDto } from './dto/update-order.dto';
import { OrderStatus, PaymentStatus, OrderType } from '@prisma/client';

@Controller('order')
export class OrderController {
  constructor(private readonly orderService: OrderService) {}

  @Post() // POST /order
  @HttpCode(HttpStatus.CREATED)
  create(@Body() createOrderDto: CreateOrderDto) {
    return this.orderService.createOrder(createOrderDto);
  }

  @Get() // GET /order?branchId=...&status=...
  findAll(
    @Query('branchId') branchId?: string,
    @Query('tableId') tableId?: string,
    @Query('customerId') customerId?: string,
    @Query('status') status?: OrderStatus,
    @Query('paymentStatus') paymentStatus?: PaymentStatus,
    @Query('orderType') orderType?: OrderType,
    @Query('startDate') startDate?: string, // YYYY-MM-DD formatında
    @Query('endDate') endDate?: string, // YYYY-MM-DD formatında
  ) {
    // Tarih stringlerini Date objelerine dönüştür
    const parsedStartDate = startDate ? new Date(startDate) : undefined;
    const parsedEndDate = endDate ? new Date(endDate + 'T23:59:59Z') : undefined; // Gün sonunu dahil et

    return this.orderService.findAllOrders(
      branchId,
      tableId,
      customerId,
      status,
      paymentStatus,
      orderType,
      parsedStartDate,
      parsedEndDate,
    );
  }

  @Get(':id') // GET /order/:id
  findOne(@Param('id') id: string) {
    return this.orderService.findOneOrder(id);
  }

  @Patch(':id') // PATCH /order/:id
  update(@Param('id') id: string, @Body() updateOrderDto: UpdateOrderDto) {
    return this.orderService.updateOrder(id, updateOrderDto);
  }

  @Delete(':id') // DELETE /order/:id
  @HttpCode(HttpStatus.NO_CONTENT)
  remove(@Param('id') id: string) {
    return this.orderService.removeOrder(id);
  }
}
