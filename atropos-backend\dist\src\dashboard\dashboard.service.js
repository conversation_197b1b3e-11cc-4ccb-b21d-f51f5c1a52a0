"use strict";
var __decorate = (this && this.__decorate) || function (decorators, target, key, desc) {
    var c = arguments.length, r = c < 3 ? target : desc === null ? desc = Object.getOwnPropertyDescriptor(target, key) : desc, d;
    if (typeof Reflect === "object" && typeof Reflect.decorate === "function") r = Reflect.decorate(decorators, target, key, desc);
    else for (var i = decorators.length - 1; i >= 0; i--) if (d = decorators[i]) r = (c < 3 ? d(r) : c > 3 ? d(target, key, r) : d(target, key)) || r;
    return c > 3 && r && Object.defineProperty(target, key, r), r;
};
var __metadata = (this && this.__metadata) || function (k, v) {
    if (typeof Reflect === "object" && typeof Reflect.metadata === "function") return Reflect.metadata(k, v);
};
Object.defineProperty(exports, "__esModule", { value: true });
exports.DashboardService = void 0;
const common_1 = require("@nestjs/common");
const prisma_service_1 = require("../prisma/prisma.service");
const client_1 = require("@prisma/client");
let DashboardService = class DashboardService {
    prisma;
    constructor(prisma) {
        this.prisma = prisma;
    }
    async getOverview(branchId) {
        const today = new Date();
        const startOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate());
        const endOfDay = new Date(today.getFullYear(), today.getMonth(), today.getDate(), 23, 59, 59);
        const yesterday = new Date(today);
        yesterday.setDate(yesterday.getDate() - 1);
        const startOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate());
        const endOfYesterday = new Date(yesterday.getFullYear(), yesterday.getMonth(), yesterday.getDate(), 23, 59, 59);
        const baseWhere = {
            deletedAt: null,
        };
        if (branchId) {
            baseWhere.branchId = branchId;
        }
        const todayOrders = await this.prisma.order.findMany({
            where: {
                ...baseWhere,
                createdAt: { gte: startOfDay, lte: endOfDay },
            },
            include: {
                payments: {
                    where: { deletedAt: null, status: { in: ['PAID', 'PARTIALLY_PAID'] } },
                },
            },
        });
        const yesterdayOrders = await this.prisma.order.findMany({
            where: {
                ...baseWhere,
                createdAt: { gte: startOfYesterday, lte: endOfYesterday },
            },
            include: {
                payments: {
                    where: { deletedAt: null, status: { in: ['PAID', 'PARTIALLY_PAID'] } },
                },
            },
        });
        const todayTotalEarning = todayOrders.reduce((total, order) => {
            const paidAmount = order.payments.reduce((sum, payment) => sum + payment.amount.toNumber(), 0);
            return total + paidAmount;
        }, 0);
        const yesterdayTotalEarning = yesterdayOrders.reduce((total, order) => {
            const paidAmount = order.payments.reduce((sum, payment) => sum + payment.amount.toNumber(), 0);
            return total + paidAmount;
        }, 0);
        const earningChange = yesterdayTotalEarning > 0
            ? ((todayTotalEarning - yesterdayTotalEarning) / yesterdayTotalEarning) * 100
            : 0;
        const inProgressOrders = await this.prisma.order.count({
            where: {
                ...baseWhere,
                status: { in: [client_1.OrderStatus.PREPARING, client_1.OrderStatus.READY, client_1.OrderStatus.SERVING] },
            },
        });
        const yesterdayInProgress = await this.prisma.order.count({
            where: {
                ...baseWhere,
                createdAt: { gte: startOfYesterday, lte: endOfYesterday },
                status: { in: [client_1.OrderStatus.PREPARING, client_1.OrderStatus.READY, client_1.OrderStatus.SERVING] },
            },
        });
        const inProgressChange = yesterdayInProgress > 0
            ? ((inProgressOrders - yesterdayInProgress) / yesterdayInProgress) * 100
            : 0;
        const waitingPaymentOrders = await this.prisma.order.count({
            where: {
                ...baseWhere,
                paymentStatus: { in: [client_1.PaymentStatus.UNPAID, client_1.PaymentStatus.PENDING] },
                status: { not: client_1.OrderStatus.CANCELLED },
            },
        });
        const yesterdayWaitingPayment = await this.prisma.order.count({
            where: {
                ...baseWhere,
                createdAt: { gte: startOfYesterday, lte: endOfYesterday },
                paymentStatus: { in: [client_1.PaymentStatus.UNPAID, client_1.PaymentStatus.PENDING] },
                status: { not: client_1.OrderStatus.CANCELLED },
            },
        });
        const waitingPaymentChange = yesterdayWaitingPayment > 0
            ? ((waitingPaymentOrders - yesterdayWaitingPayment) / yesterdayWaitingPayment) * 100
            : 0;
        return {
            totalEarning: parseFloat(todayTotalEarning.toFixed(2)),
            totalEarningChange: parseFloat(earningChange.toFixed(1)),
            inProgress: inProgressOrders,
            inProgressChange: parseFloat(inProgressChange.toFixed(1)),
            waitingList: waitingPaymentOrders,
            waitingListChange: parseFloat(waitingPaymentChange.toFixed(1)),
        };
    }
};
exports.DashboardService = DashboardService;
exports.DashboardService = DashboardService = __decorate([
    (0, common_1.Injectable)(),
    __metadata("design:paramtypes", [prisma_service_1.PrismaService])
], DashboardService);
//# sourceMappingURL=dashboard.service.js.map