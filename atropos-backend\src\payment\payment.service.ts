// src/payment/payment.service.ts
import { Injectable, NotFoundException, BadRequestException, ConflictException } from '@nestjs/common';
import { PrismaService } from '../prisma/prisma.service';
import { CreatePaymentDto } from './dto/create-payment.dto';
import { UpdatePaymentDto, RefundPaymentDto } from './dto/update-payment.dto';
import { PaymentStatus, CashMovementType } from '@prisma/client'; // İlgili enum'ları import et

@Injectable()
export class PaymentService {
  constructor(private prisma: PrismaService) {}

  async createPayment(data: CreatePaymentDto) {
    // Sipariş ve Ödeme Yöntemi mevcut mu kontrol et
    const order = await this.prisma.order.findUnique({
      where: { id: data.orderId, deletedAt: null },
      include: { branch: { select: { companyId: true } } },
    });
    if (!order) {
      throw new NotFoundException(`Order with ID "${data.orderId}" not found.`);
    }

    const paymentMethod = await this.prisma.paymentMethod.findUnique({
      where: { id: data.paymentMethodId, deletedAt: null },
    });
    if (!paymentMethod) {
      throw new NotFoundException(`Payment method with ID "${data.paymentMethodId}" not found.`);
    }

    // Siparişin zaten ödenen miktarını kontrol et
    const totalPaidForOrder = await this.prisma.payment.aggregate({
      where: { orderId: data.orderId, status: { in: ['PAID', 'PARTIALLY_PAID'] }, deletedAt: null },
      _sum: { amount: true },
    });
    const currentlyPaid = totalPaidForOrder._sum.amount?.toNumber() || 0;
    const remainingAmount = order.totalAmount.toNumber() - currentlyPaid;

    if (data.amount > remainingAmount + 0.01) { // Küçük bir tolerans ekle
      throw new BadRequestException(`Payment amount (${data.amount}) exceeds remaining order total (${remainingAmount.toFixed(2)}).`);
    }

    const paymentStatus: PaymentStatus = data.amount >= remainingAmount ? 'PAID' : 'PARTIALLY_PAID';

    let cashMovementId: string | undefined;

    // Nakit ödeme ise kasa hareketi oluştur (İleride CashMovement modülü geldiğinde burası aktifleşecek)
    // Şimdilik sadece sembolik olarak bırakalım
    if (paymentMethod.type === 'CASH') {
      const cashMovement = await this.prisma.cashMovement.create({
        data: {
          branchId: order.branchId, // Siparişin şube ID'si
          userId: order.cashierId || order.waiterId || (await this.prisma.user.findFirst({ where: { companyId: order.branch.companyId, role: 'ADMIN' } }))?.id || 'system-user-id', // Kim yaptı
          type: CashMovementType.SALE,
          amount: parseFloat(data.amount.toFixed(2)),
          description: `Order ${order.orderNumber} Payment`,
          referenceId: order.id,
          referenceType: 'ORDER',
          paymentMethodId: paymentMethod.id,
          // Bu alanlar şimdilik geçici, gerçek kasa bakiyesi yönetimi CashMovement modülünde olacak
          previousBalance: 0,
          currentBalance: 0,
        },
      });
      cashMovementId = cashMovement.id;
    }

    const payment = await this.prisma.payment.create({
      data: {
        ...data,
        status: paymentStatus,
        amount: parseFloat(data.amount.toFixed(2)),
        tipAmount: data.tipAmount !== undefined ? parseFloat(data.tipAmount.toFixed(2)) : undefined,
        changeAmount: data.changeAmount !== undefined ? parseFloat(data.changeAmount.toFixed(2)) : undefined,
        installments: data.installments,
        cashMovementId: cashMovementId, // Kasa hareketi ID'si
      },
      include: { order: true, paymentMethod: true },
    });

    // Siparişin toplam ödenen miktarını ve PaymentStatus'unu güncelle
    await this.prisma.order.update({
      where: { id: order.id },
      data: {
        paidAmount: parseFloat((currentlyPaid + data.amount).toFixed(2)),
        paymentStatus: paymentStatus,
        // Eğer sipariş tamamen ödendiyse OrderStatus'u da 'COMPLETED' yapabiliriz,
        // ancak bu genellikle ayrı bir iş kuralıdır. Şimdilik sadece paymentStatus.
      },
    });

    return payment;
  }

  async findAllPayments(orderId?: string, paymentMethodId?: string, status?: PaymentStatus) {
    return this.prisma.payment.findMany({
      where: {
        orderId: orderId || undefined,
        paymentMethodId: paymentMethodId || undefined,
        status: status || undefined,
        deletedAt: null,
      },
      include: {
        order: { select: { id: true, orderNumber: true, totalAmount: true, paymentStatus: true } },
        paymentMethod: { select: { id: true, name: true, code: true } },
      },
      orderBy: { paidAt: 'desc' },
    });
  }

  async findOnePayment(id: string) {
    const payment = await this.prisma.payment.findUnique({
      where: { id, deletedAt: null },
      include: {
        order: { select: { id: true, orderNumber: true, totalAmount: true, paymentStatus: true } },
        paymentMethod: { select: { id: true, name: true, code: true } },
        cashMovement: true, // CashMovement bilgilerini de dahil et
      },
    });
    if (!payment) {
      throw new NotFoundException(`Payment with ID "${id}" not found.`);
    }
    return payment;
  }

  // Güncelleme yerine refund (iade) veya void (iptal) mantığı
  async refundPayment(id: string, refundDto: RefundPaymentDto) {
    const payment = await this.prisma.payment.findUnique({
      where: { id, deletedAt: null },
      include: { order: { include: { branch: { select: { companyId: true } } } }, paymentMethod: true },
    });
    if (!payment) {
      throw new NotFoundException(`Payment with ID "${id}" not found.`);
    }
    if (payment.status === 'REFUNDED' || payment.status === 'PARTIALLY_REFUNDED') {
      throw new BadRequestException('Payment has already been refunded.');
    }
    if (refundDto.refundAmount > payment.amount.toNumber()) {
      throw new BadRequestException('Refund amount cannot exceed original payment amount.');
    }

    const newStatus: PaymentStatus = refundDto.refundAmount === payment.amount.toNumber() ? 'REFUNDED' : 'PARTIALLY_REFUNDED';

    let cashMovementId: string | undefined;
    // Nakit iade ise kasa hareketi oluştur
    if (payment.paymentMethod.type === 'CASH') {
        const cashMovement = await this.prisma.cashMovement.create({
            data: {
                branchId: payment.order.branchId,
                userId: refundDto.refundedBy || (await this.prisma.user.findFirst({ where: { companyId: payment.order.branch.companyId, role: 'ADMIN' } }))?.id || 'system-user-id',
                type: CashMovementType.REFUND,
                amount: parseFloat(refundDto.refundAmount.toFixed(2)) * -1, // Gider olarak kaydet
                description: `Refund for Order ${payment.order.orderNumber} Payment ${payment.id}`,
                referenceId: payment.order.id,
                referenceType: 'ORDER',
                paymentMethodId: payment.paymentMethodId,
                previousBalance: 0,
                currentBalance: 0,
            },
        });
        cashMovementId = cashMovement.id;
    }


    const refundedPayment = await this.prisma.payment.update({
      where: { id },
      data: {
        status: newStatus,
        refundAmount: parseFloat(((payment.refundAmount?.toNumber() || 0) + refundDto.refundAmount).toFixed(2)),
        refundReason: refundDto.refundReason,
        refundedAt: new Date(),
        cashMovementId: cashMovementId,
      },
      include: { order: true, paymentMethod: true },
    });

    // Siparişin ödenen miktarını ve paymentStatus'unu güncelle
    const totalPaidForOrderAfterRefund = await this.prisma.payment.aggregate({
        where: { orderId: payment.orderId, status: { in: ['PAID', 'PARTIALLY_PAID'] }, deletedAt: null },
        _sum: { amount: true },
    });
    const currentlyPaidAfterRefund = totalPaidForOrderAfterRefund._sum.amount?.toNumber() || 0;

    // Ödenen miktardan iade edilen miktarı çıkararak güncel paidAmount'ı bul
    // Bu hesaplama biraz daha karmaşık olabilir, çünkü kısmi iadeler var
    // Basitçe: Siparişin totalAmount'ına göre ödeme durumunu belirle
    let orderPaymentStatus: PaymentStatus;
    if (currentlyPaidAfterRefund <= 0) {
        orderPaymentStatus = 'UNPAID';
    } else if (currentlyPaidAfterRefund >= payment.order.totalAmount.toNumber() - 0.01) {
        orderPaymentStatus = 'PAID';
    } else {
        orderPaymentStatus = 'PARTIALLY_PAID';
    }

    await this.prisma.order.update({
        where: { id: payment.orderId },
        data: {
            paidAmount: parseFloat(currentlyPaidAfterRefund.toFixed(2)), // Güncel ödenen miktar
            paymentStatus: orderPaymentStatus,
        },
    });

    return refundedPayment;
  }

  async removePayment(id: string) {
    // Soft delete uygulaması (genellikle ödemeler fiziksel silinmez, ancak şema gereği eklendi)
    // Normalde burada refund/void işlemi tercih edilir.
    try {
      const payment = await this.prisma.payment.update({
        where: { id, deletedAt: null },
        data: { deletedAt: new Date(), status: 'VOIDED' }, // VOIDED durumuna getir
      });

      // Siparişin toplam ödenen miktarını ve PaymentStatus'unu yeniden hesapla
      // Bu kısım, remove'un refund gibi davrandığı veya bir ödemenin tamamen geçersiz kılındığı senaryoya göre değişir.
      // Basitçe, ödenen miktarı çıkararak siparişin paidAmount'ını ve paymentStatus'unu güncelle.
      const totalPaidForOrder = await this.prisma.payment.aggregate({
          where: { orderId: payment.orderId, status: { in: ['PAID', 'PARTIALLY_PAID'] }, deletedAt: null }, // Sadece geçerli ödemeleri dikkate al
          _sum: { amount: true },
      });
      const currentlyPaid = totalPaidForOrder._sum.amount?.toNumber() || 0;
      const order = await this.prisma.order.findUnique({ where: { id: payment.orderId } });

      let orderPaymentStatus: PaymentStatus;
      if (currentlyPaid <= 0) {
          orderPaymentStatus = 'UNPAID';
      } else if (order && currentlyPaid >= order.totalAmount.toNumber() - 0.01) {
          orderPaymentStatus = 'PAID';
      } else {
          orderPaymentStatus = 'PARTIALLY_PAID';
      }

      if (order) {
        await this.prisma.order.update({
            where: { id: order.id },
            data: {
                paidAmount: parseFloat(currentlyPaid.toFixed(2)),
                paymentStatus: orderPaymentStatus,
            },
        });
      }

      return payment;
    } catch (error) {
      if (error.code === 'P2025') {
        throw new NotFoundException(`Payment with ID "${id}" not found or already deleted.`);
      }
      throw error;
    }
  }
}
