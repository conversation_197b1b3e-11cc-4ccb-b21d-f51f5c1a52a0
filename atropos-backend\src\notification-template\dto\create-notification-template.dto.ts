// src/notification-template/dto/create-notification-template.dto.ts
import {
  IsString,
  IsNotEmpty,
  IsOptional,
  IsEnum,
  IsInt,
  Min,
  Max,
  IsBoolean,
} from 'class-validator';
import { Type } from 'class-transformer';
import { NotificationChannel } from '@prisma/client'; // Enum'ı import et

export class CreateNotificationTemplateDto {
  @IsString()
  @IsNotEmpty()
  companyId: string;

  @IsString()
  @IsNotEmpty()
  name: string;

  @IsString()
  @IsNotEmpty()
  code: string; // Kampanya kodu (benzersiz olmalı) - ORDER_READY, RESERVATION_CONFIRM

  @IsEnum(NotificationChannel)
  @IsNotEmpty()
  channel: NotificationChannel; // SMS, EMAIL, PUSH_NOTIFICATION, IN_APP

  @IsString()
  @IsOptional()
  subject?: string; // Email için

  @IsString()
  @IsNotEmpty()
  content: string; // {customerName} değişkenlerini destekler

  @IsInt()
  @Min(0)
  @IsOptional()
  smsLength?: number; // Kara<PERSON>er sayısı (SMS için)

  @IsInt()
  @Min(0)
  @IsOptional()
  smsCredits?: number; // SMS kredisi (SMS için)

  @IsString()
  @IsOptional()
  sendTiming?: string; // "immediate", "scheduled"

  @IsInt()
  @Min(0)
  @IsOptional()
  sendDelay?: number; // Dakika gecikme

  @IsBoolean()
  @IsOptional()
  active?: boolean;
}
